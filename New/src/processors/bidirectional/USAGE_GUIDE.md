# Bidirectional Custom Field Synchronization Usage Guide

This guide demonstrates how to use the comprehensive bidirectional custom field synchronization system between AutoPatient (AP) and CliniCore (CC) systems.

## Overview

The bidirectional synchronization system provides:

- **Field Mapping**: Convert field definitions between AP and CC formats
- **Value Conversion**: Convert field values with reversible transformations
- **Type Compatibility**: Handle field type mismatches gracefully
- **Production Ready**: Comprehensive error handling and logging

## Quick Start

```typescript
import {
  mapApToCcCustomFields,
  mapCcToApCustomFields,
  convertApCustomFieldValueToCcCustomField,
  convertCcCustomFieldValueToApCustomField
} from '@/processors/bidirectional';
```

## Field Mapping

### AP to CC Field Mapping

Convert AutoPatient field definitions to CliniCore format:

```typescript
import { apCustomfield } from '@/apiClient';
import { mapApToCcCustomFields } from '@/processors/bidirectional';

async function syncApFieldsToCC(requestId: string) {
  // Get AP custom field definitions
  const apFields = await apCustomfield.all();
  
  // Map to CC format
  const ccFieldDefinitions = mapApToCcCustomFields(apFields, requestId);
  
  // Create CC fields using the API client
  for (const ccFieldDef of ccFieldDefinitions) {
    try {
      const createdField = await ccCustomfieldReq.create(ccFieldDef);
      console.log(`Created CC field: ${createdField.name}`);
    } catch (error) {
      console.error(`Failed to create CC field: ${error.message}`);
    }
  }
}
```

### CC to AP Field Mapping

Convert CliniCore field definitions to AutoPatient format:

```typescript
import { ccCustomfieldReq } from '@/apiClient';
import { mapCcToApCustomFields } from '@/processors/bidirectional';

async function syncCcFieldsToAP(requestId: string) {
  // Get CC custom field definitions
  const ccFields = await ccCustomfieldReq.all();
  
  // Map to AP format
  const apFieldDefinitions = mapCcToApCustomFields(ccFields, requestId);
  
  // Create AP fields using the API client
  for (const apFieldDef of apFieldDefinitions) {
    try {
      const createdField = await apCustomfield.create(apFieldDef);
      console.log(`Created AP field: ${createdField.name}`);
    } catch (error) {
      console.error(`Failed to create AP field: ${error.message}`);
    }
  }
}
```

## Value Conversion

### AP to CC Value Conversion

Convert AutoPatient field values to CliniCore format:

```typescript
import {
  convertApCustomFieldValueToCcCustomField,
  areTypesCompatible
} from '@/processors/bidirectional';

function convertApValueToCC(
  apValue: string,
  apField: APGetCustomFieldType,
  ccField: GetCCCustomField,
  requestId: string
): string {
  // Check type compatibility (optional)
  if (!areTypesCompatible(apField.dataType.toLowerCase(), ccField.type.toLowerCase())) {
    console.warn(`Type mismatch: ${apField.dataType} → ${ccField.type}`);
  }
  
  // Convert value
  const convertedValue = convertApCustomFieldValueToCcCustomField(
    apValue,
    apField,
    ccField,
    requestId
  );
  
  return convertedValue;
}

// Example usage
const apBooleanValue = "Yes";
const ccBooleanValue = convertApValueToCC(
  apBooleanValue,
  apBooleanField,
  ccBooleanField,
  "request-123"
);
// Result: "true"
```

### CC to AP Value Conversion

Convert CliniCore field values to AutoPatient format:

```typescript
import { convertCcCustomFieldValueToApCustomField } from '@/processors/bidirectional';

function convertCcValueToAP(
  ccValue: string,
  ccField: GetCCCustomField,
  apField: APGetCustomFieldType,
  requestId: string
): string {
  const convertedValue = convertCcCustomFieldValueToApCustomField(
    ccValue,
    ccField,
    apField,
    requestId
  );
  
  return convertedValue;
}

// Example usage
const ccBooleanValue = "true";
const apBooleanValue = convertCcValueToAP(
  ccBooleanValue,
  ccBooleanField,
  apBooleanField,
  "request-123"
);
// Result: "Yes"
```

## Field Type Compatibility

### Supported Type Conversions

| AP Type | CC Type | Conversion |
|---------|---------|------------|
| TEXT | text | Direct |
| TEXTAREA | textarea | Direct |
| RADIO (Yes/No) | boolean | Yes/No ↔ true/false |
| RADIO (options) | select | Direct |
| MULTIPLE_OPTIONS | multiselect | Comma-separated ↔ Array |
| PHONE | phone/telephone | Direct (handles mismatch) |
| EMAIL | email | Direct |
| NUMBER | number/decimal | Direct |

### Graceful Type Mismatch Handling

The system handles type mismatches gracefully:

```typescript
import { validateApToCcCompatibility } from '@/processors/bidirectional';

// This will log a warning but allow conversion
const isCompatible = validateApToCcCompatibility(
  phoneApField,    // PHONE type
  textCcField,     // text type (mismatch)
  requestId
);
// Returns: true (with warning logged)
```

## Complete Synchronization Example

Here's a complete example of bidirectional synchronization:

```typescript
import {
  mapApToCcCustomFields,
  convertApCustomFieldValueToCcCustomField,
  mapCcToApCustomFields,
  convertCcCustomFieldValueToApCustomField
} from '@/processors/bidirectional';

async function bidirectionalSync(requestId: string) {
  try {
    // 1. Get field definitions from both systems
    const apFields = await apCustomfield.all();
    const ccFields = await ccCustomfieldReq.all();
    
    // 2. Map AP fields to CC format and create missing CC fields
    const ccFieldMappings = mapApToCcCustomFields(apFields, requestId);
    for (const ccFieldDef of ccFieldMappings) {
      // Check if field already exists, create if not
      const existingField = ccFields.find(f => f.name === ccFieldDef.name);
      if (!existingField) {
        await ccCustomfieldReq.create(ccFieldDef);
      }
    }
    
    // 3. Map CC fields to AP format and create missing AP fields
    const apFieldMappings = mapCcToApCustomFields(ccFields, requestId);
    for (const apFieldDef of apFieldMappings) {
      // Check if field already exists, create if not
      const existingField = apFields.find(f => f.name === apFieldDef.name);
      if (!existingField) {
        await apCustomfield.create(apFieldDef);
      }
    }
    
    // 4. Sync field values (example for AP → CC)
    const apContact = await contactReq.get("contact-id");
    const ccPatient = await patientReq.get(123);
    
    // Convert and sync custom field values
    for (const apField of apFields) {
      const apValue = apContact.customFields?.[apField.fieldKey];
      if (apValue) {
        const ccField = ccFields.find(f => f.name === apField.name.toLowerCase());
        if (ccField) {
          const convertedValue = convertApCustomFieldValueToCcCustomField(
            apValue,
            apField,
            ccField,
            requestId
          );
          
          // Update CC patient with converted value
          await patientReq.update(ccPatient.id, {
            customFields: {
              [ccField.id]: convertedValue
            }
          });
        }
      }
    }
    
    console.log("Bidirectional synchronization completed successfully");
  } catch (error) {
    console.error("Synchronization failed:", error);
    throw error;
  }
}
```

## Error Handling

The system provides comprehensive error handling:

```typescript
try {
  const convertedValue = convertApCustomFieldValueToCcCustomField(
    apValue,
    apField,
    ccField,
    requestId
  );
} catch (error) {
  // Error is logged automatically
  // Original value is returned as fallback
  console.error("Conversion failed, using original value");
}
```

## Performance Considerations

- **Caching**: Field definitions are cached automatically by the API clients
- **Batch Processing**: Process multiple fields in batches for better performance
- **Lazy Loading**: Only fetch field definitions when needed
- **Error Recovery**: Individual field failures don't stop the entire sync process

## Best Practices

1. **Always use request IDs** for proper logging and debugging
2. **Handle type mismatches gracefully** - the system logs warnings but continues
3. **Validate field definitions** before creating new fields
4. **Use batch operations** when syncing multiple fields
5. **Monitor logs** for type compatibility warnings and conversion issues
6. **Test reversible transformations** to ensure data integrity

## Troubleshooting

### Common Issues

1. **Type Mismatch Warnings**: These are normal for compatible but different types (e.g., PHONE/telephone)
2. **Field Creation Failures**: Check field name validation and API permissions
3. **Value Conversion Errors**: Original values are used as fallback
4. **Missing Field Definitions**: Ensure both systems have the required custom fields

### Debug Logging

Enable debug logging to see detailed conversion information:

```typescript
import { logDebug } from '@/utils/logger';

// Debug logs show:
// - Field type compatibility checks
// - Value conversions with before/after values
// - Field mapping results
// - Error details with context
```
