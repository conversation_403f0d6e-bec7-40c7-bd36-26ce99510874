/**
 * Example Usage of Comprehensive Patient Custom Field Synchronization
 *
 * This file demonstrates how to use the new updateApToCcCustomFields and
 * updateCcToApCustomFields methods in real-world scenarios.
 *
 * These examples show the main entry points for custom field synchronization
 * that leverage the enhanced bidirectional synchronization system.
 */

import type { PatientInstance } from "./patientSyncMethods";
import { updateApToCcCustomFields, updateCcToApCustomFields } from "./patientSyncMethods";
import { patientReq, contactReq } from "@/apiClient";
import { logInfo, logError } from "@/utils/logger";

/**
 * Example 1: Simple AP to CC Synchronization
 * 
 * This example shows how to sync custom fields from AutoPatient to CliniCore
 * when you have a patient instance from the database.
 */
export async function exampleApToCcSync(patientInstance: PatientInstance, requestId: string) {
	try {
		logInfo(requestId, `Starting AP to CC sync for patient ${patientInstance.id}`);

		// Use the comprehensive sync method
		const ccCustomFieldPayload = await updateApToCc<PERSON>ustomFields(patientInstance, requestId);

		// Check if we have data to sync and a valid CC patient ID
		if (ccCustomFieldPayload.length > 0 && patientInstance.ccId) {
			// Update the CC patient with the custom fields
			const updatedPatient = await patientReq.update(patientInstance.ccId, {
				customFields: ccCustomFieldPayload,
			});

			logInfo(
				requestId,
				`Successfully synced ${ccCustomFieldPayload.length} custom fields to CC patient ${patientInstance.ccId}`,
			);

			return updatedPatient;
		} else {
			logInfo(requestId, "No custom fields to sync or missing CC patient ID");
			return null;
		}
	} catch (error) {
		logError(requestId, "Failed to sync AP to CC custom fields:", error);
		throw error;
	}
}

/**
 * Example 2: Simple CC to AP Synchronization
 * 
 * This example shows how to sync custom fields from CliniCore to AutoPatient
 * with proper separation of standard fields and custom fields.
 */
export async function exampleCcToApSync(patientInstance: PatientInstance, requestId: string) {
	try {
		logInfo(requestId, `Starting CC to AP sync for patient ${patientInstance.id}`);

		// Use the comprehensive sync method
		const apSyncResult = await updateCcToApCustomFields(patientInstance, requestId);

		// Check if we have data to sync and a valid AP contact ID
		const hasStandardFields = Object.keys(apSyncResult.standardFields).length > 0;
		const hasCustomFields = apSyncResult.customFields.length > 0;

		if ((hasStandardFields || hasCustomFields) && patientInstance.apId) {
			// Combine standard fields and custom fields for AP update
			const updatePayload = {
				...apSyncResult.standardFields,
				customFields: apSyncResult.customFields,
			};

			// Update the AP contact
			const updatedContact = await contactReq.update(patientInstance.apId, updatePayload);

			logInfo(
				requestId,
				`Successfully synced ${Object.keys(apSyncResult.standardFields).length} standard fields and ${apSyncResult.customFields.length} custom fields to AP contact ${patientInstance.apId}`,
			);

			return updatedContact;
		} else {
			logInfo(requestId, "No fields to sync or missing AP contact ID");
			return null;
		}
	} catch (error) {
		logError(requestId, "Failed to sync CC to AP custom fields:", error);
		throw error;
	}
}

/**
 * Example 3: Bidirectional Synchronization
 * 
 * This example shows how to perform complete bidirectional sync,
 * ensuring both systems have the latest custom field data.
 */
export async function exampleBidirectionalSync(patientInstance: PatientInstance, requestId: string) {
	const results = {
		apToCcSuccess: false,
		ccToApSuccess: false,
		errors: [] as string[],
	};

	try {
		logInfo(requestId, `Starting bidirectional sync for patient ${patientInstance.id}`);

		// Sync AP data to CC (if both exist)
		if (patientInstance.apData && patientInstance.ccId) {
			try {
				await exampleApToCcSync(patientInstance, requestId);
				results.apToCcSuccess = true;
				logInfo(requestId, "AP to CC sync completed successfully");
			} catch (error) {
				const errorMsg = `AP to CC sync failed: ${error instanceof Error ? error.message : String(error)}`;
				results.errors.push(errorMsg);
				logError(requestId, errorMsg);
			}
		}

		// Sync CC data to AP (if both exist)
		if (patientInstance.ccData && patientInstance.apId) {
			try {
				await exampleCcToApSync(patientInstance, requestId);
				results.ccToApSuccess = true;
				logInfo(requestId, "CC to AP sync completed successfully");
			} catch (error) {
				const errorMsg = `CC to AP sync failed: ${error instanceof Error ? error.message : String(error)}`;
				results.errors.push(errorMsg);
				logError(requestId, errorMsg);
			}
		}

		logInfo(
			requestId,
			`Bidirectional sync completed. AP→CC: ${results.apToCcSuccess}, CC→AP: ${results.ccToApSuccess}, Errors: ${results.errors.length}`,
		);

		return results;
	} catch (error) {
		logError(requestId, "Bidirectional sync failed:", error);
		throw error;
	}
}

/**
 * Example 4: Webhook Integration Pattern
 * 
 * This example shows how to integrate the sync methods into webhook handlers
 * for real-time synchronization.
 */
export async function exampleWebhookIntegration(
	patientInstance: PatientInstance,
	sourceSystem: "AP" | "CC",
	requestId: string,
) {
	try {
		logInfo(
			requestId,
			`Processing ${sourceSystem} webhook for patient ${patientInstance.id}`,
		);

		if (sourceSystem === "AP") {
			// AP webhook received - sync to CC if CC patient exists
			if (patientInstance.ccId) {
				await exampleApToCcSync(patientInstance, requestId);
				logInfo(requestId, "AP webhook data synced to CC");
			} else {
				logInfo(requestId, "AP webhook received but no CC patient to sync to");
			}
		} else if (sourceSystem === "CC") {
			// CC webhook received - sync to AP if AP contact exists
			if (patientInstance.apId) {
				await exampleCcToApSync(patientInstance, requestId);
				logInfo(requestId, "CC webhook data synced to AP");
			} else {
				logInfo(requestId, "CC webhook received but no AP contact to sync to");
			}
		}
	} catch (error) {
		logError(requestId, `Webhook integration failed for ${sourceSystem}:`, error);
		// In webhook scenarios, you might want to handle errors gracefully
		// rather than throwing, depending on your error handling strategy
	}
}

/**
 * Example 5: Batch Processing Pattern
 * 
 * This example shows how to process multiple patients efficiently
 * with proper error handling and progress tracking.
 */
export async function exampleBatchSync(
	patientInstances: PatientInstance[],
	direction: "AP_TO_CC" | "CC_TO_AP" | "BIDIRECTIONAL",
	requestId: string,
) {
	const results = {
		total: patientInstances.length,
		successful: 0,
		failed: 0,
		errors: [] as { patientId: string; error: string }[],
	};

	logInfo(requestId, `Starting batch ${direction} sync for ${results.total} patients`);

	for (const patient of patientInstances) {
		try {
			switch (direction) {
				case "AP_TO_CC":
					await exampleApToCcSync(patient, requestId);
					break;
				case "CC_TO_AP":
					await exampleCcToApSync(patient, requestId);
					break;
				case "BIDIRECTIONAL":
					await exampleBidirectionalSync(patient, requestId);
					break;
			}
			results.successful++;
		} catch (error) {
			results.failed++;
			results.errors.push({
				patientId: patient.id,
				error: error instanceof Error ? error.message : String(error),
			});
			logError(requestId, `Batch sync failed for patient ${patient.id}:`, error);
		}
	}

	logInfo(
		requestId,
		`Batch sync completed. Success: ${results.successful}, Failed: ${results.failed}`,
	);

	return results;
}

/**
 * Example 6: Data Validation Pattern
 * 
 * This example shows how to validate data before syncing
 * and handle edge cases gracefully.
 */
export async function exampleValidatedSync(patientInstance: PatientInstance, requestId: string) {
	try {
		// Validate patient instance
		if (!patientInstance.id) {
			throw new Error("Patient instance missing ID");
		}

		// Check for AP to CC sync requirements
		if (patientInstance.apData && patientInstance.ccId) {
			// Validate AP data has custom fields
			if (patientInstance.apData.customFields && patientInstance.apData.customFields.length > 0) {
				logInfo(requestId, `Syncing ${patientInstance.apData.customFields.length} AP custom fields to CC`);
				await exampleApToCcSync(patientInstance, requestId);
			} else {
				logInfo(requestId, "No AP custom fields to sync");
			}
		}

		// Check for CC to AP sync requirements
		if (patientInstance.ccData && patientInstance.apId) {
			// Validate CC data has custom fields
			if (patientInstance.ccData.customFields && patientInstance.ccData.customFields.length > 0) {
				logInfo(requestId, `Syncing ${patientInstance.ccData.customFields.length} CC custom fields to AP`);
				await exampleCcToApSync(patientInstance, requestId);
			} else {
				logInfo(requestId, "No CC custom fields to sync");
			}
		}

		logInfo(requestId, "Validated sync completed successfully");
	} catch (error) {
		logError(requestId, "Validated sync failed:", error);
		throw error;
	}
}
