/**
 * Bidirectional Consistency Validator
 *
 * Provides comprehensive validation logic to ensure that custom field values maintain
 * integrity through round-trip synchronization (AP→CC→AP and CC→AP→CC). This module
 * implements consistency checks and validation for the complete bidirectional sync process.
 *
 * Features:
 * - Round-trip value integrity validation
 * - Field mapping consistency checks
 * - Data transformation reversibility testing
 * - Comprehensive sync process validation
 * - Performance-optimized consistency checking
 */

import type {
	APGetCustomFieldType,
	GetAPContactType,
	GetCCCustomField,
	GetCCPatientCustomField,
} from "@type";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import {
	convertApCustomFieldValueToCcCustomField,
	convertCcCustomFieldValueToApCustomField,
} from "./valueConverter";
import {
	checkFieldTypeCompatibility,
	validateApToCcCompatibility,
	validateCcToApCompatibility,
} from "./typeCompatibility";

/**
 * Interface for round-trip validation result
 */
export interface RoundTripValidationResult {
	/** Whether the round-trip maintained value integrity */
	isConsistent: boolean;
	/** Original value before round-trip */
	originalValue: string;
	/** Value after round-trip conversion */
	roundTripValue: string;
	/** Field name being tested */
	fieldName: string;
	/** Direction of the round-trip test */
	direction: "AP→CC→AP" | "CC→AP→CC";
	/** Detailed error message if inconsistent */
	errorMessage?: string;
	/** Whether the inconsistency is acceptable (e.g., formatting differences) */
	isAcceptableInconsistency: boolean;
	/** Transformation details */
	transformationDetails: {
		firstConversion: string;
		secondConversion: string;
		transformationsApplied: string[];
	};
}

/**
 * Interface for field mapping consistency result
 */
export interface FieldMappingConsistencyResult {
	/** Whether field mappings are consistent */
	isConsistent: boolean;
	/** Field name being tested */
	fieldName: string;
	/** AP field definition */
	apField: APGetCustomFieldType;
	/** CC field definition */
	ccField: GetCCCustomField;
	/** Compatibility issues found */
	compatibilityIssues: string[];
	/** Whether the mapping can be used despite issues */
	isUsable: boolean;
}

/**
 * Interface for comprehensive sync validation result
 */
export interface SyncValidationResult {
	/** Overall validation success */
	isValid: boolean;
	/** Round-trip validation results */
	roundTripResults: RoundTripValidationResult[];
	/** Field mapping consistency results */
	fieldMappingResults: FieldMappingConsistencyResult[];
	/** Summary statistics */
	summary: {
		totalFieldsTested: number;
		consistentFields: number;
		inconsistentFields: number;
		acceptableInconsistencies: number;
		criticalIssues: number;
	};
	/** Recommendations for improving sync reliability */
	recommendations: string[];
}

/**
 * Validate round-trip value integrity for AP→CC→AP conversion
 *
 * Tests whether a value maintains its essential meaning and data integrity
 * when converted from AP to CC and back to AP.
 *
 * @param originalValue - Original AP custom field value
 * @param apField - AP custom field definition
 * @param ccField - CC custom field definition
 * @param requestId - Request ID for logging
 * @returns Round-trip validation result
 */
export function validateApToCcToApRoundTrip(
	originalValue: string,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string,
): RoundTripValidationResult {
	logDebug(
		requestId,
		`Testing AP→CC→AP round-trip for field "${apField.name}" with value: "${originalValue.substring(0, 50)}${originalValue.length > 50 ? "..." : ""}"`,
	);

	try {
		// Step 1: Convert AP → CC
		const ccValue = convertApCustomFieldValueToCcCustomField(
			originalValue,
			apField,
			ccField,
			requestId,
		);

		// Step 2: Convert CC → AP
		const roundTripValue = convertCcCustomFieldValueToApCustomField(
			ccValue,
			ccField,
			apField,
			requestId,
		);

		// Step 3: Analyze consistency
		const isConsistent = areValuesConsistent(
			originalValue,
			roundTripValue,
			apField.dataType,
		);

		const isAcceptableInconsistency = !isConsistent && 
			isAcceptableFormatDifference(originalValue, roundTripValue, apField.dataType);

		const result: RoundTripValidationResult = {
			isConsistent,
			originalValue,
			roundTripValue,
			fieldName: apField.name,
			direction: "AP→CC→AP",
			isAcceptableInconsistency,
			transformationDetails: {
				firstConversion: ccValue,
				secondConversion: roundTripValue,
				transformationsApplied: getTransformationsApplied(originalValue, ccValue, roundTripValue),
			},
		};

		if (!isConsistent && !isAcceptableInconsistency) {
			result.errorMessage = `Value integrity lost: "${originalValue}" → "${ccValue}" → "${roundTripValue}"`;
			logWarn(requestId, result.errorMessage);
		} else if (isAcceptableInconsistency) {
			logDebug(
				requestId,
				`Acceptable formatting difference in round-trip for field "${apField.name}": "${originalValue}" → "${roundTripValue}"`,
			);
		} else {
			logDebug(
				requestId,
				`Round-trip validation passed for field "${apField.name}"`,
			);
		}

		return result;
	} catch (error) {
		logError(
			requestId,
			`Error during AP→CC→AP round-trip validation for field "${apField.name}":`,
			error,
		);

		return {
			isConsistent: false,
			originalValue,
			roundTripValue: "",
			fieldName: apField.name,
			direction: "AP→CC→AP",
			errorMessage: `Validation error: ${error}`,
			isAcceptableInconsistency: false,
			transformationDetails: {
				firstConversion: "",
				secondConversion: "",
				transformationsApplied: ["error"],
			},
		};
	}
}

/**
 * Validate round-trip value integrity for CC→AP→CC conversion
 *
 * Tests whether a value maintains its essential meaning and data integrity
 * when converted from CC to AP and back to CC.
 *
 * @param originalValue - Original CC custom field value
 * @param ccField - CC custom field definition
 * @param apField - AP custom field definition
 * @param requestId - Request ID for logging
 * @returns Round-trip validation result
 */
export function validateCcToApToCcRoundTrip(
	originalValue: string,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string,
): RoundTripValidationResult {
	logDebug(
		requestId,
		`Testing CC→AP→CC round-trip for field "${ccField.name}" with value: "${originalValue.substring(0, 50)}${originalValue.length > 50 ? "..." : ""}"`,
	);

	try {
		// Step 1: Convert CC → AP
		const apValue = convertCcCustomFieldValueToApCustomField(
			originalValue,
			ccField,
			apField,
			requestId,
		);

		// Step 2: Convert AP → CC
		const roundTripValue = convertApCustomFieldValueToCcCustomField(
			apValue,
			apField,
			ccField,
			requestId,
		);

		// Step 3: Analyze consistency
		const isConsistent = areValuesConsistent(
			originalValue,
			roundTripValue,
			ccField.type,
		);

		const isAcceptableInconsistency = !isConsistent && 
			isAcceptableFormatDifference(originalValue, roundTripValue, ccField.type);

		const result: RoundTripValidationResult = {
			isConsistent,
			originalValue,
			roundTripValue,
			fieldName: ccField.name,
			direction: "CC→AP→CC",
			isAcceptableInconsistency,
			transformationDetails: {
				firstConversion: apValue,
				secondConversion: roundTripValue,
				transformationsApplied: getTransformationsApplied(originalValue, apValue, roundTripValue),
			},
		};

		if (!isConsistent && !isAcceptableInconsistency) {
			result.errorMessage = `Value integrity lost: "${originalValue}" → "${apValue}" → "${roundTripValue}"`;
			logWarn(requestId, result.errorMessage);
		} else if (isAcceptableInconsistency) {
			logDebug(
				requestId,
				`Acceptable formatting difference in round-trip for field "${ccField.name}": "${originalValue}" → "${roundTripValue}"`,
			);
		} else {
			logDebug(
				requestId,
				`Round-trip validation passed for field "${ccField.name}"`,
			);
		}

		return result;
	} catch (error) {
		logError(
			requestId,
			`Error during CC→AP→CC round-trip validation for field "${ccField.name}":`,
			error,
		);

		return {
			isConsistent: false,
			originalValue,
			roundTripValue: "",
			fieldName: ccField.name,
			direction: "CC→AP→CC",
			errorMessage: `Validation error: ${error}`,
			isAcceptableInconsistency: false,
			transformationDetails: {
				firstConversion: "",
				secondConversion: "",
				transformationsApplied: ["error"],
			},
		};
	}
}

/**
 * Validate field mapping consistency between AP and CC fields
 *
 * Checks whether the field definitions are compatible and can be reliably
 * synchronized in both directions.
 *
 * @param apField - AP custom field definition
 * @param ccField - CC custom field definition
 * @param requestId - Request ID for logging
 * @returns Field mapping consistency result
 */
export function validateFieldMappingConsistency(
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string,
): FieldMappingConsistencyResult {
	const compatibilityIssues: string[] = [];
	let isUsable = true;

	logDebug(
		requestId,
		`Validating field mapping consistency: AP "${apField.name}" (${apField.dataType}) ↔ CC "${ccField.name}" (${ccField.type})`,
	);

	// Check AP→CC compatibility
	const apToCcCompatible = validateApToCcCompatibility(apField, ccField, requestId);
	if (!apToCcCompatible) {
		compatibilityIssues.push(`AP→CC conversion may have issues: ${apField.dataType} → ${ccField.type}`);
	}

	// Check CC→AP compatibility
	const ccToApCompatible = validateCcToApCompatibility(ccField, apField, requestId);
	if (!ccToApCompatible) {
		compatibilityIssues.push(`CC→AP conversion may have issues: ${ccField.type} → ${apField.dataType}`);
	}

	// Check for critical incompatibilities
	const apToCcResult = checkFieldTypeCompatibility(
		apField.dataType,
		ccField.type,
		apField.name,
		"AP→CC",
		requestId,
	);

	const ccToApResult = checkFieldTypeCompatibility(
		ccField.type,
		apField.dataType,
		ccField.name,
		"CC→AP",
		requestId,
	);

	if (apToCcResult.compatibilityLevel === "incompatible" || 
		ccToApResult.compatibilityLevel === "incompatible") {
		compatibilityIssues.push("Critical type incompatibility detected");
		isUsable = false;
	}

	// Check for field name similarity (helps with debugging)
	if (!areFieldNamesRelated(apField.name, ccField.name)) {
		compatibilityIssues.push("Field names appear unrelated - verify mapping is correct");
	}

	const isConsistent = compatibilityIssues.length === 0;

	logDebug(
		requestId,
		`Field mapping consistency result: ${isConsistent ? "consistent" : "has issues"} (${compatibilityIssues.length} issues found)`,
	);

	return {
		isConsistent,
		fieldName: `${apField.name} ↔ ${ccField.name}`,
		apField,
		ccField,
		compatibilityIssues,
		isUsable,
	};
}

/**
 * Check if two values are consistent (semantically equivalent)
 *
 * @param originalValue - Original value
 * @param convertedValue - Value after round-trip conversion
 * @param fieldType - Field type for context
 * @returns True if values are consistent
 */
function areValuesConsistent(
	originalValue: string,
	convertedValue: string,
	fieldType: string,
): boolean {
	// Exact match
	if (originalValue === convertedValue) {
		return true;
	}

	// Normalize for comparison
	const normalizedOriginal = normalizeValueForComparison(originalValue, fieldType);
	const normalizedConverted = normalizeValueForComparison(convertedValue, fieldType);

	return normalizedOriginal === normalizedConverted;
}

/**
 * Check if value differences are acceptable formatting differences
 *
 * @param originalValue - Original value
 * @param convertedValue - Converted value
 * @param fieldType - Field type for context
 * @returns True if differences are acceptable
 */
function isAcceptableFormatDifference(
	originalValue: string,
	convertedValue: string,
	fieldType: string,
): boolean {
	const fieldTypeLower = fieldType.toLowerCase();

	// Phone number formatting differences
	if (fieldTypeLower.includes("phone") || fieldTypeLower.includes("telephone")) {
		const originalDigits = originalValue.replace(/\D/g, "");
		const convertedDigits = convertedValue.replace(/\D/g, "");
		return originalDigits === convertedDigits;
	}

	// Email case differences
	if (fieldTypeLower.includes("email")) {
		return originalValue.toLowerCase() === convertedValue.toLowerCase();
	}

	// Number formatting differences
	if (fieldTypeLower.includes("number") || fieldTypeLower.includes("decimal")) {
		const originalNum = parseFloat(originalValue);
		const convertedNum = parseFloat(convertedValue);
		return !isNaN(originalNum) && !isNaN(convertedNum) && originalNum === convertedNum;
	}

	// Boolean value differences (Yes/No vs true/false)
	if (fieldTypeLower.includes("boolean") || fieldTypeLower.includes("radio")) {
		return areBooleanValuesEquivalent(originalValue, convertedValue);
	}

	// Date formatting differences
	if (fieldTypeLower.includes("date") || fieldTypeLower.includes("time")) {
		try {
			const originalDate = new Date(originalValue);
			const convertedDate = new Date(convertedValue);
			return originalDate.getTime() === convertedDate.getTime();
		} catch {
			return false;
		}
	}

	// Whitespace differences for text fields
	if (fieldTypeLower.includes("text") || fieldTypeLower.includes("textarea")) {
		return originalValue.trim() === convertedValue.trim();
	}

	return false;
}

/**
 * Normalize value for comparison
 *
 * @param value - Value to normalize
 * @param fieldType - Field type for context
 * @returns Normalized value
 */
function normalizeValueForComparison(value: string, fieldType: string): string {
	const fieldTypeLower = fieldType.toLowerCase();

	// Phone numbers: keep only digits
	if (fieldTypeLower.includes("phone") || fieldTypeLower.includes("telephone")) {
		return value.replace(/\D/g, "");
	}

	// Email: lowercase
	if (fieldTypeLower.includes("email")) {
		return value.toLowerCase().trim();
	}

	// Numbers: parse and stringify to normalize format
	if (fieldTypeLower.includes("number") || fieldTypeLower.includes("decimal")) {
		const num = parseFloat(value);
		return isNaN(num) ? value : num.toString();
	}

	// Boolean: normalize to true/false
	if (fieldTypeLower.includes("boolean") || fieldTypeLower.includes("radio")) {
		return normalizeBooleanValue(value);
	}

	// Default: trim whitespace
	return value.trim();
}

/**
 * Check if boolean values are equivalent
 *
 * @param value1 - First boolean value
 * @param value2 - Second boolean value
 * @returns True if values are equivalent
 */
function areBooleanValuesEquivalent(value1: string, value2: string): boolean {
	const normalized1 = normalizeBooleanValue(value1);
	const normalized2 = normalizeBooleanValue(value2);
	return normalized1 === normalized2;
}

/**
 * Normalize boolean value to true/false
 *
 * @param value - Boolean value to normalize
 * @returns Normalized boolean string
 */
function normalizeBooleanValue(value: string): string {
	const normalizedValue = value.toLowerCase().trim();

	if (["true", "yes", "1", "on", "enabled"].includes(normalizedValue)) {
		return "true";
	} else if (["false", "no", "0", "off", "disabled"].includes(normalizedValue)) {
		return "false";
	}

	return value; // Return original if not recognizable
}

/**
 * Get list of transformations applied during conversion
 *
 * @param originalValue - Original value
 * @param intermediateValue - Value after first conversion
 * @param finalValue - Value after second conversion
 * @returns Array of transformation descriptions
 */
function getTransformationsApplied(
	originalValue: string,
	intermediateValue: string,
	finalValue: string,
): string[] {
	const transformations: string[] = [];

	if (originalValue !== intermediateValue) {
		transformations.push(`First conversion: "${originalValue}" → "${intermediateValue}"`);
	}

	if (intermediateValue !== finalValue) {
		transformations.push(`Second conversion: "${intermediateValue}" → "${finalValue}"`);
	}

	if (transformations.length === 0) {
		transformations.push("No transformations applied");
	}

	return transformations;
}

/**
 * Check if field names are related (similar or likely to be the same field)
 *
 * @param apFieldName - AP field name
 * @param ccFieldName - CC field name
 * @returns True if field names appear related
 */
function areFieldNamesRelated(apFieldName: string, ccFieldName: string): boolean {
	const normalizedAp = apFieldName.toLowerCase().replace(/[^a-z0-9]/g, "");
	const normalizedCc = ccFieldName.toLowerCase().replace(/[^a-z0-9]/g, "");

	// Exact match after normalization
	if (normalizedAp === normalizedCc) {
		return true;
	}

	// Check if one contains the other
	if (normalizedAp.includes(normalizedCc) || normalizedCc.includes(normalizedAp)) {
		return true;
	}

	// Check for common variations
	const commonVariations = [
		["phone", "telephone", "mobile"],
		["email", "mail", "emailaddress"],
		["firstname", "fname", "givenname"],
		["lastname", "lname", "surname", "familyname"],
		["dateofbirth", "dob", "birthdate"],
		["address", "street", "streetaddress"],
		["postalcode", "zipcode", "zip"],
	];

	for (const variations of commonVariations) {
		if (variations.includes(normalizedAp) && variations.includes(normalizedCc)) {
			return true;
		}
	}

	return false;
}

/**
 * Perform comprehensive sync validation for a set of field mappings
 *
 * Tests multiple field mappings for consistency and provides detailed
 * validation results with recommendations.
 *
 * @param fieldMappings - Array of AP-CC field mapping pairs
 * @param testValues - Array of test values to use for round-trip testing
 * @param requestId - Request ID for logging
 * @returns Comprehensive sync validation result
 */
export function performComprehensiveSyncValidation(
	fieldMappings: Array<{ apField: APGetCustomFieldType; ccField: GetCCCustomField }>,
	testValues: string[],
	requestId: string,
): SyncValidationResult {
	logInfo(
		requestId,
		`Starting comprehensive sync validation for ${fieldMappings.length} field mappings with ${testValues.length} test values`,
	);

	const roundTripResults: RoundTripValidationResult[] = [];
	const fieldMappingResults: FieldMappingConsistencyResult[] = [];
	const recommendations: string[] = [];

	// Test each field mapping
	for (const { apField, ccField } of fieldMappings) {
		// Validate field mapping consistency
		const mappingResult = validateFieldMappingConsistency(apField, ccField, requestId);
		fieldMappingResults.push(mappingResult);

		// Test round-trip conversions with sample values
		for (const testValue of testValues) {
			// Test AP→CC→AP round-trip
			const apRoundTrip = validateApToCcToApRoundTrip(
				testValue,
				apField,
				ccField,
				requestId,
			);
			roundTripResults.push(apRoundTrip);

			// Test CC→AP→CC round-trip
			const ccRoundTrip = validateCcToApToCcRoundTrip(
				testValue,
				ccField,
				apField,
				requestId,
			);
			roundTripResults.push(ccRoundTrip);
		}
	}

	// Calculate summary statistics
	const totalFieldsTested = fieldMappings.length;
	const consistentFields = fieldMappingResults.filter(r => r.isConsistent).length;
	const inconsistentFields = totalFieldsTested - consistentFields;

	const consistentRoundTrips = roundTripResults.filter(r => r.isConsistent).length;
	const acceptableInconsistencies = roundTripResults.filter(
		r => !r.isConsistent && r.isAcceptableInconsistency
	).length;
	const criticalIssues = roundTripResults.filter(
		r => !r.isConsistent && !r.isAcceptableInconsistency
	).length;

	// Generate recommendations
	if (criticalIssues > 0) {
		recommendations.push(
			`${criticalIssues} critical round-trip issues found. Review field type mappings and value conversion logic.`
		);
	}

	if (inconsistentFields > 0) {
		recommendations.push(
			`${inconsistentFields} field mappings have compatibility issues. Consider reviewing field type assignments.`
		);
	}

	if (acceptableInconsistencies > 0) {
		recommendations.push(
			`${acceptableInconsistencies} acceptable formatting differences found. Monitor for user impact.`
		);
	}

	const unusableFields = fieldMappingResults.filter(r => !r.isUsable).length;
	if (unusableFields > 0) {
		recommendations.push(
			`${unusableFields} field mappings are not usable due to critical incompatibilities. These should be excluded from sync.`
		);
	}

	if (recommendations.length === 0) {
		recommendations.push("All field mappings and round-trip conversions are working correctly.");
	}

	const isValid = criticalIssues === 0 && unusableFields === 0;

	const result: SyncValidationResult = {
		isValid,
		roundTripResults,
		fieldMappingResults,
		summary: {
			totalFieldsTested,
			consistentFields,
			inconsistentFields,
			acceptableInconsistencies,
			criticalIssues,
		},
		recommendations,
	};

	logInfo(
		requestId,
		`Sync validation completed: ${isValid ? "PASSED" : "FAILED"} - ${consistentRoundTrips}/${roundTripResults.length} round-trips consistent, ${criticalIssues} critical issues`,
	);

	return result;
}

/**
 * Generate standard test values for round-trip validation
 *
 * @returns Array of test values covering common data patterns
 */
export function generateStandardTestValues(): string[] {
	return [
		// Text values
		"Simple text",
		"Text with spaces and punctuation!",
		"Multi-line\ntext\nwith\nbreaks",

		// Phone numbers
		"******-123-4567",
		"(*************",
		"5551234567",

		// Email addresses
		"<EMAIL>",
		"<EMAIL>",

		// Numbers
		"123",
		"123.45",
		"-67.89",
		"0",

		// Boolean values
		"Yes",
		"No",
		"true",
		"false",
		"1",
		"0",

		// Dates
		"2024-01-15",
		"01/15/2024",
		"2024-01-15T10:30:00Z",

		// Special characters
		"Text with émojis 🎉 and ñ special chars",
		"HTML <b>tags</b> & entities &amp;",

		// Empty and edge cases
		"",
		" ",
		"   whitespace   ",
	];
}
