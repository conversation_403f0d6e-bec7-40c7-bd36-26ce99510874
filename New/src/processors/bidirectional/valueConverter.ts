/**
 * Bidirectional Value Conversion Module
 *
 * Provides comprehensive value conversion between AutoPatient (AP) and CliniCore (CC) custom fields.
 * Implements reversible transformations that maintain data integrity and handle field type mismatches
 * gracefully while ensuring bidirectional synchronization accuracy.
 *
 * Features:
 * - AP to CC custom field value conversion
 * - CC to AP custom field value conversion
 * - Reversible boolean transformations (Yes/No ↔ true/false)
 * - Multi-value field handling with proper formatting
 * - Field type compatibility validation
 * - Graceful error handling with fallback values
 * - Production-ready logging and monitoring
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { logDebug, logError, logWarn } from "@/utils/logger";
import {
	areTypesCompatible,
	isApBooleanRadioField,
	isCcBooleanField,
	validateApToCcCompatibility,
	validateCcToApCompatibility,
} from "./typeCompatibility";

/**
 * Convert AutoPatient custom field value to CliniCore format
 *
 * This is the primary function for converting AP custom field values to CC format.
 * It handles all field types and implements bidirectional conversion tracking with
 * reversible transformations.
 *
 * @param apValue - AP custom field value to convert
 * @param apField - AP custom field definition
 * @param ccField - CC custom field definition
 * @param requestId - Request ID for logging
 * @returns Converted value suitable for CC custom field
 */
export function convertApCustomFieldValueToCcCustomField(
	apValue: string,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string,
): string {
	// Input validation
	if (!apValue || apValue.trim().length === 0) {
		logDebug(requestId, `Empty AP value for field "${apField.name}"`);
		return "";
	}

	const trimmedValue = apValue.trim();

	try {
		// Validate field type compatibility
		if (!validateApToCcCompatibility(apField, ccField, requestId)) {
			logWarn(
				requestId,
				`Type compatibility issue for field "${apField.name}". Using fallback conversion.`,
			);
		}

		let convertedValue = trimmedValue;

		// Handle boolean RADIO fields (AP Yes/No → CC true/false)
		if (isApBooleanRadioField(apField) && isCcBooleanField(ccField)) {
			convertedValue = convertApBooleanToCc(trimmedValue);
		}
		// Handle multi-value fields (AP comma-separated → CC formatted)
		else if (
			apField.dataType === "MULTIPLE_OPTIONS" &&
			(ccField.type.toLowerCase().includes("multiselect") ||
				ccField.type.toLowerCase().includes("checkbox"))
		) {
			convertedValue = convertApMultiValueToCc(trimmedValue, ccField.type);
		}
		// Handle phone number formatting (enhanced with international support)
		else if (
			apField.dataType === "PHONE" &&
			(ccField.type.toLowerCase() === "phone" || ccField.type.toLowerCase() === "telephone")
		) {
			convertedValue = normalizePhoneNumber(trimmedValue);
		}
		// Handle email formatting (enhanced validation)
		else if (apField.dataType === "EMAIL" && ccField.type.toLowerCase() === "email") {
			convertedValue = normalizeEmail(trimmedValue);
		}
		// Handle number formatting (enhanced with decimal support)
		else if (
			apField.dataType === "NUMBER" &&
			(ccField.type.toLowerCase() === "number" ||
				ccField.type.toLowerCase() === "decimal" ||
				ccField.type.toLowerCase() === "float" ||
				ccField.type.toLowerCase() === "integer")
		) {
			convertedValue = normalizeNumber(trimmedValue);
		}
		// Handle date formatting (enhanced with multiple formats)
		else if (
			apField.dataType === "DATE" &&
			(ccField.type.toLowerCase() === "date" ||
				ccField.type.toLowerCase() === "datetime")
		) {
			convertedValue = normalizeDateValue(trimmedValue, ccField.type);
		}
		// Handle time formatting
		else if (
			apField.dataType === "TIME" &&
			(ccField.type.toLowerCase() === "time" ||
				ccField.type.toLowerCase() === "datetime")
		) {
			convertedValue = normalizeTimeValue(trimmedValue, ccField.type);
		}
		// Handle datetime formatting
		else if (
			apField.dataType === "DATETIME" &&
			ccField.type.toLowerCase() === "datetime"
		) {
			convertedValue = normalizeDateTimeValue(trimmedValue);
		}
		// Handle text area formatting (preserve line breaks)
		else if (
			apField.dataType === "TEXTAREA" &&
			(ccField.type.toLowerCase() === "textarea" ||
				ccField.type.toLowerCase() === "text")
		) {
			convertedValue = normalizeTextAreaValue(trimmedValue, ccField.type);
		}
		// Handle URL/website formatting
		else if (
			(apField.dataType === "TEXT" || apField.dataType === "TEXTAREA") &&
			(ccField.type.toLowerCase() === "url" ||
				ccField.type.toLowerCase() === "website")
		) {
			convertedValue = normalizeUrlValue(trimmedValue);
		}
		// Handle compatible type mismatches (PHONE/telephone, TEXT/textarea)
		else if (isCompatibleTypeMismatch(apField.dataType, ccField.type)) {
			convertedValue = handleCompatibleTypeMismatch(trimmedValue, apField.dataType, ccField.type, requestId);
		}
		// Handle direct pass-through for compatible types
		else {
			convertedValue = trimmedValue;
		}

		// Log the conversion for debugging
		logValueConversion("AP→CC", apField.name, apField.dataType, ccField.type, trimmedValue, convertedValue, requestId);

		return convertedValue;
	} catch (error) {
		logError(
			requestId,
			`Error converting AP value for field "${apField.name}":`,
			error,
		);
		// Return original value as fallback
		return trimmedValue;
	}
}

/**
 * Convert CliniCore custom field value to AutoPatient format
 *
 * This is the primary function for converting CC custom field values to AP format.
 * It implements the inverse conversions of convertApCustomFieldValueToCcCustomField
 * to ensure reversible transformations.
 *
 * @param ccValue - CC custom field value to convert
 * @param ccField - CC custom field definition
 * @param apField - AP custom field definition
 * @param requestId - Request ID for logging
 * @returns Converted value suitable for AP custom field
 */
export function convertCcCustomFieldValueToApCustomField(
	ccValue: string,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string,
): string {
	// Input validation
	if (!ccValue || ccValue.trim().length === 0) {
		logDebug(requestId, `Empty CC value for field "${ccField.name}"`);
		return "";
	}

	const trimmedValue = ccValue.trim();

	try {
		// Validate field type compatibility
		if (!validateCcToApCompatibility(ccField, apField, requestId)) {
			logWarn(
				requestId,
				`Type compatibility issue for field "${ccField.name}". Using fallback conversion.`,
			);
		}

		let convertedValue = trimmedValue;

		// Handle boolean fields (CC true/false → AP Yes/No)
		if (isCcBooleanField(ccField) && isApBooleanRadioField(apField)) {
			convertedValue = convertCcBooleanToAp(trimmedValue);
		}
		// Handle multi-value fields (CC formatted → AP comma-separated)
		else if (
			(ccField.type.toLowerCase().includes("multiselect") ||
				ccField.type.toLowerCase().includes("checkbox")) &&
			apField.dataType === "MULTIPLE_OPTIONS"
		) {
			convertedValue = convertCcMultiValueToAp(trimmedValue);
		}
		// Handle phone number formatting (enhanced with international support)
		else if (
			(ccField.type.toLowerCase() === "phone" || ccField.type.toLowerCase() === "telephone") &&
			apField.dataType === "PHONE"
		) {
			convertedValue = normalizePhoneNumber(trimmedValue);
		}
		// Handle email formatting (enhanced validation)
		else if (ccField.type.toLowerCase() === "email" && apField.dataType === "EMAIL") {
			convertedValue = normalizeEmail(trimmedValue);
		}
		// Handle number formatting (enhanced with decimal support)
		else if (
			(ccField.type.toLowerCase() === "number" ||
				ccField.type.toLowerCase() === "decimal" ||
				ccField.type.toLowerCase() === "float" ||
				ccField.type.toLowerCase() === "integer") &&
			apField.dataType === "NUMBER"
		) {
			convertedValue = normalizeNumber(trimmedValue);
		}
		// Handle date formatting (enhanced with multiple formats)
		else if (
			(ccField.type.toLowerCase() === "date" ||
				ccField.type.toLowerCase() === "datetime") &&
			apField.dataType === "DATE"
		) {
			convertedValue = normalizeDateValue(trimmedValue, "date");
		}
		// Handle time formatting
		else if (
			(ccField.type.toLowerCase() === "time" ||
				ccField.type.toLowerCase() === "datetime") &&
			apField.dataType === "TIME"
		) {
			convertedValue = normalizeTimeValue(trimmedValue, "time");
		}
		// Handle datetime formatting
		else if (
			ccField.type.toLowerCase() === "datetime" &&
			apField.dataType === "DATETIME"
		) {
			convertedValue = normalizeDateTimeValue(trimmedValue);
		}
		// Handle text area formatting (preserve line breaks)
		else if (
			(ccField.type.toLowerCase() === "textarea" ||
				ccField.type.toLowerCase() === "text") &&
			apField.dataType === "TEXTAREA"
		) {
			convertedValue = normalizeTextAreaValue(trimmedValue, "textarea");
		}
		// Handle URL/website formatting
		else if (
			(ccField.type.toLowerCase() === "url" ||
				ccField.type.toLowerCase() === "website") &&
			(apField.dataType === "TEXT" || apField.dataType === "TEXTAREA")
		) {
			convertedValue = normalizeUrlValue(trimmedValue);
		}
		// Handle compatible type mismatches (PHONE/telephone, TEXT/textarea)
		else if (isCompatibleTypeMismatch(ccField.type, apField.dataType)) {
			convertedValue = handleCompatibleTypeMismatch(trimmedValue, ccField.type, apField.dataType, requestId);
		}
		// Handle direct pass-through for compatible types
		else {
			convertedValue = trimmedValue;
		}

		// Log the conversion for debugging
		logValueConversion("CC→AP", ccField.name, ccField.type, apField.dataType, trimmedValue, convertedValue, requestId);

		return convertedValue;
	} catch (error) {
		logError(
			requestId,
			`Error converting CC value for field "${ccField.name}":`,
			error,
		);
		// Return original value as fallback
		return trimmedValue;
	}
}

/**
 * Convert AP boolean RADIO field value to CC boolean value
 * Implements the inverse of convertCcBooleanToAp for reversible transformations
 *
 * @param apValue - AP field value ("Yes" or "No")
 * @returns CC boolean value ("true" or "false")
 */
export function convertApBooleanToCc(apValue: string): string {
	const normalizedValue = apValue.toLowerCase().trim();

	// Convert AP Yes/No to CC boolean
	if (normalizedValue === "yes" || normalizedValue === "true" || normalizedValue === "1") {
		return "true";
	} else if (normalizedValue === "no" || normalizedValue === "false" || normalizedValue === "0") {
		return "false";
	}

	// Default to false for any other value (safe fallback)
	return "false";
}

/**
 * Convert CC boolean field value to AP RADIO value
 * Implements the inverse of convertApBooleanToCc for reversible transformations
 *
 * @param ccValue - CC field value ("true" or "false")
 * @returns AP RADIO value ("Yes" or "No")
 */
export function convertCcBooleanToAp(ccValue: string): string {
	const normalizedValue = ccValue.toLowerCase().trim();

	// Convert CC boolean to AP Yes/No
	if (normalizedValue === "true" || normalizedValue === "1" || normalizedValue === "yes") {
		return "Yes";
	} else if (normalizedValue === "false" || normalizedValue === "0" || normalizedValue === "no") {
		return "No";
	}

	// Default to No for any other value (safe fallback)
	return "No";
}

/**
 * Convert AP multi-value field to CC format
 * Handles MULTIPLE_OPTIONS fields that may have comma-separated values
 *
 * @param apValue - AP field value (potentially comma-separated)
 * @param ccFieldType - CC field type to determine output format
 * @returns Formatted value for CC field
 */
export function convertApMultiValueToCc(apValue: string, ccFieldType: string): string {
	const normalizedType = ccFieldType.toLowerCase();

	// For multiselect fields, ensure proper comma separation
	if (normalizedType.includes("multiselect") || normalizedType.includes("checkbox")) {
		// Split by comma, trim each value, and rejoin with consistent formatting
		const values = apValue
			.split(",")
			.map((v) => v.trim())
			.filter((v) => v.length > 0);
		return values.join(", ");
	}

	// For other field types, return as-is
	return apValue.trim();
}

/**
 * Convert CC multi-value field to AP format
 * Implements the inverse of convertApMultiValueToCc for reversible transformations
 *
 * @param ccValue - CC field value (formatted multi-value)
 * @returns AP MULTIPLE_OPTIONS value (comma-separated)
 */
export function convertCcMultiValueToAp(ccValue: string): string {
	// Split by comma, trim each value, and rejoin with consistent formatting
	const values = ccValue
		.split(",")
		.map((v) => v.trim())
		.filter((v) => v.length > 0);
	return values.join(", ");
}

/**
 * Normalize phone number format
 *
 * @param phoneValue - Phone number value
 * @returns Normalized phone number
 */
function normalizePhoneNumber(phoneValue: string): string {
	// Remove all non-digit characters except + for international numbers
	const cleaned = phoneValue.replace(/[^\d+]/g, "");
	
	// Basic validation - ensure it's not empty after cleaning
	if (cleaned.length === 0) {
		return phoneValue; // Return original if cleaning results in empty string
	}
	
	return cleaned;
}

/**
 * Normalize email format
 *
 * @param emailValue - Email value
 * @returns Normalized email
 */
function normalizeEmail(emailValue: string): string {
	return emailValue.toLowerCase().trim();
}

/**
 * Normalize number format
 *
 * @param numberValue - Number value
 * @returns Normalized number
 */
function normalizeNumber(numberValue: string): string {
	// Remove any non-numeric characters except decimal point and minus sign
	const cleaned = numberValue.replace(/[^\d.-]/g, "");
	
	// Basic validation
	if (cleaned.length === 0 || isNaN(Number(cleaned))) {
		return numberValue; // Return original if not a valid number
	}
	
	return cleaned;
}

/**
 * Normalize date value with multiple format support
 *
 * @param dateValue - Date value to normalize
 * @param targetType - Target field type (date or datetime)
 * @returns Normalized date value
 */
function normalizeDateValue(dateValue: string, targetType: string): string {
	try {
		const date = new Date(dateValue);
		if (isNaN(date.getTime())) {
			return dateValue; // Return original if invalid date
		}

		// Format based on target type
		if (targetType.toLowerCase() === "datetime") {
			return date.toISOString();
		} else {
			// Return date only in ISO format (YYYY-MM-DD)
			return date.toISOString().split('T')[0];
		}
	} catch (error) {
		return dateValue; // Return original on error
	}
}

/**
 * Normalize time value
 *
 * @param timeValue - Time value to normalize
 * @param targetType - Target field type
 * @returns Normalized time value
 */
function normalizeTimeValue(timeValue: string, targetType: string): string {
	try {
		// Handle various time formats (HH:MM, HH:MM:SS, etc.)
		const timeRegex = /^(\d{1,2}):(\d{2})(?::(\d{2}))?$/;
		const match = timeValue.match(timeRegex);

		if (match) {
			const hours = match[1].padStart(2, '0');
			const minutes = match[2];
			const seconds = match[3] || '00';

			if (targetType.toLowerCase() === "datetime") {
				// For datetime, we need a full datetime value
				const today = new Date().toISOString().split('T')[0];
				return `${today}T${hours}:${minutes}:${seconds}`;
			} else {
				return `${hours}:${minutes}:${seconds}`;
			}
		}

		return timeValue; // Return original if no match
	} catch (error) {
		return timeValue; // Return original on error
	}
}

/**
 * Normalize datetime value
 *
 * @param datetimeValue - Datetime value to normalize
 * @returns Normalized datetime value in ISO format
 */
function normalizeDateTimeValue(datetimeValue: string): string {
	try {
		const date = new Date(datetimeValue);
		if (isNaN(date.getTime())) {
			return datetimeValue; // Return original if invalid
		}
		return date.toISOString();
	} catch (error) {
		return datetimeValue; // Return original on error
	}
}

/**
 * Normalize textarea value (preserve formatting)
 *
 * @param textValue - Text value to normalize
 * @param targetType - Target field type
 * @returns Normalized text value
 */
function normalizeTextAreaValue(textValue: string, targetType: string): string {
	// For textarea to text conversion, preserve content but may need to handle line breaks
	if (targetType.toLowerCase() === "text") {
		// Convert line breaks to spaces for single-line text fields
		return textValue.replace(/\r?\n/g, ' ').replace(/\s+/g, ' ').trim();
	}

	// For textarea to textarea, preserve formatting
	return textValue;
}

/**
 * Normalize URL value
 *
 * @param urlValue - URL value to normalize
 * @returns Normalized URL value
 */
function normalizeUrlValue(urlValue: string): string {
	try {
		// Add protocol if missing
		if (urlValue && !urlValue.match(/^https?:\/\//i)) {
			return `https://${urlValue}`;
		}
		return urlValue;
	} catch (error) {
		return urlValue; // Return original on error
	}
}

/**
 * Check if field types are compatible mismatches that should be allowed
 *
 * @param apType - AP field type
 * @param ccType - CC field type
 * @returns True if types are compatible mismatches
 */
function isCompatibleTypeMismatch(apType: string, ccType: string): boolean {
	const apTypeLower = apType.toLowerCase();
	const ccTypeLower = ccType.toLowerCase();

	// Define compatible type mismatches
	const compatibleMismatches = [
		// PHONE/telephone compatibility
		{ ap: "phone", cc: "telephone" },
		{ ap: "phone", cc: "text" },
		// TEXT/textarea compatibility
		{ ap: "text", cc: "textarea" },
		{ ap: "textarea", cc: "text" },
		// EMAIL variations
		{ ap: "email", cc: "text" },
		{ ap: "text", cc: "email" },
		// NUMBER variations
		{ ap: "number", cc: "text" },
		{ ap: "text", cc: "number" },
	];

	return compatibleMismatches.some(
		mismatch =>
			(apTypeLower === mismatch.ap && ccTypeLower === mismatch.cc) ||
			(apTypeLower === mismatch.cc && ccTypeLower === mismatch.ap)
	);
}

/**
 * Handle compatible type mismatches with appropriate conversion
 *
 * @param value - Value to convert
 * @param apType - AP field type
 * @param ccType - CC field type
 * @param requestId - Request ID for logging
 * @returns Converted value
 */
function handleCompatibleTypeMismatch(
	value: string,
	apType: string,
	ccType: string,
	requestId: string
): string {
	const apTypeLower = apType.toLowerCase();
	const ccTypeLower = ccType.toLowerCase();

	logDebug(
		requestId,
		`Handling compatible type mismatch: ${apType} → ${ccType} for value: "${value.substring(0, 50)}${value.length > 50 ? "..." : ""}"`
	);

	// Apply appropriate conversion based on target type
	if (ccTypeLower === "phone" || ccTypeLower === "telephone") {
		return normalizePhoneNumber(value);
	} else if (ccTypeLower === "email") {
		return normalizeEmail(value);
	} else if (ccTypeLower === "number") {
		return normalizeNumber(value);
	} else if (ccTypeLower === "textarea") {
		return value; // Preserve formatting for textarea
	} else if (ccTypeLower === "text") {
		return normalizeTextAreaValue(value, "text");
	}

	// Default: return value as-is
	return value;
}

/**
 * Log value conversion for debugging and monitoring
 *
 * @param direction - Conversion direction ("AP→CC" or "CC→AP")
 * @param fieldName - Field name
 * @param sourceType - Source field type
 * @param targetType - Target field type
 * @param originalValue - Original value before conversion
 * @param convertedValue - Converted value after conversion
 * @param requestId - Request ID for logging
 */
function logValueConversion(
	direction: string,
	fieldName: string,
	sourceType: string,
	targetType: string,
	originalValue: string,
	convertedValue: string,
	requestId: string,
): void {
	if (originalValue !== convertedValue) {
		logDebug(
			requestId,
			`${direction} value conversion for "${fieldName}" (${sourceType}→${targetType}): "${originalValue}" → "${convertedValue}"`,
		);
	} else {
		logDebug(
			requestId,
			`${direction} value pass-through for "${fieldName}" (${sourceType}→${targetType}): "${originalValue}"`,
		);
	}
}
