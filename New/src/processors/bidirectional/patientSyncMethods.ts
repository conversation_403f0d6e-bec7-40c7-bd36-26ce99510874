/**
 * Comprehensive Patient Custom Field Synchronization Methods
 *
 * This module provides the main entry points for complete custom field synchronization
 * between AutoPatient (AP) and CliniCore (CC) systems using the enhanced bidirectional
 * synchronization system.
 *
 * Features:
 * - Complete AP to CC custom field synchronization with standard field mapping
 * - Complete CC to AP custom field synchronization with standard/custom field separation
 * - Leverages enhanced bidirectional value extraction, conversion, and validation
 * - <PERSON>les standard field mapping issue (CC custom fields → AP standard fields)
 * - Production-ready with comprehensive error handling and logging
 * - TypeScript compliant with strict type safety
 * - Performance optimized following existing codebase patterns
 *
 * Usage:
 * ```typescript
 * import { updateApToCcCustomFields, updateCcToApCustomFields } from '@/processors/bidirectional/patientSyncMethods';
 *
 * // Sync AP data to CC format
 * const ccPayload = await updateApToCcCustomFields(patientInstance, requestId);
 * await patientReq.update(ccPatientId, { customFields: ccPayload });
 *
 * // Sync CC data to AP format
 * const apPayload = await updateCcToApCustomFields(patientInstance, requestId);
 * await contactReq.update(apContactId, { ...apPayload.standardFields, customFields: apPayload.customFields });
 * ```
 */
import { dbSchema, getDb } from "@database";
import type {
	APGetCustomFieldType,
	GetCCCustomField,
	PostCCPatientCustomfield,
} from "@type";
import { apCustomfield, ccCustomfieldReq, contactReq, patientReq } from "@/apiClient";
import { eq } from "drizzle-orm";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import {
	extractApCustomFieldValues,
	extractCcCustomFieldValues,
	extractApStandardFieldValues,
	createApCustomFieldPayload,
	createCcCustomFieldPayload,
	createApStandardFieldPayload,
	type CcCustomFieldValue,
} from "./valueHandler";
import {
	CC_TO_AP_STANDARD_FIELD_MAPPING,
	getCCFieldVariations,
} from "./fieldMappingConstants";

/**
 * Patient instance type inferred from database schema
 * Automatically stays in sync with database schema definition
 */
export type PatientInstance = typeof dbSchema.patient.$inferSelect;



/**
 * Update AP to CC Custom Fields - Complete Synchronization Flow
 *
 * Implements the complete AP → CC synchronization flow including fresh data fetching,
 * field creation, API updates, and local database updates.
 *
 * @param patientInstance - Local database patient instance with apId and ccId
 * @param requestId - Required request ID for logging and tracing
 * @returns Promise<PatientInstance> - Updated patient instance with fresh data
 *
 * @example
 * ```typescript
 * const updatedPatient = await updateApToCcCustomFields(patient, "req-123");
 * ```javascript
*/
export async function updateApToCcCustomFields(
	patientInstance: PatientInstance,
	requestId: string,
): Promise<PatientInstance> {
	logInfo(requestId, `Starting complete AP to CC custom field synchronization for patient ${patientInstance.id}`);

	try {
		// Step 1: Validate input data
		if (!patientInstance.apId) {
			throw new Error(`Patient ${patientInstance.id} missing AP ID - cannot fetch fresh AP data`);
		}
		if (!patientInstance.ccId) {
			throw new Error(`Patient ${patientInstance.id} missing CC ID - cannot update CC patient`);
		}

		// Step 2: Fetch fresh AP data from API
		logDebug(requestId, `Fetching fresh AP contact data for AP ID: ${patientInstance.apId}`);
		const freshApContactData = await contactReq.get(patientInstance.apId);
		logDebug(requestId, `Fetched fresh AP contact data with ${freshApContactData.customFields?.length || 0} custom fields`);

		// Step 3: Fetch AP field definitions
		logDebug(requestId, "Fetching AP custom field definitions");
		const apCustomFields = await apCustomfield.all();
		logDebug(requestId, `Fetched ${apCustomFields.length} AP custom field definitions`);

		// Step 4: Extract and convert AP custom field values
		logDebug(requestId, "Extracting AP custom field values using enhanced system");
		const apCustomFieldValues = await extractApCustomFieldValues(
			freshApContactData,
			apCustomFields,
			requestId,
		);

		// Step 5: Extract AP standard field values for CC custom field mapping
		logDebug(requestId, "Extracting AP standard field values for CC mapping");
		const apStandardFieldValues = extractApStandardFieldValues(freshApContactData, requestId);

		// Step 6: Get CC custom field definitions
		logDebug(requestId, "Fetching CC custom field definitions");
		let ccCustomFields = await ccCustomfieldReq.all();
		logDebug(requestId, `Fetched ${ccCustomFields.length} CC custom field definitions`);

		// Step 7: Create CC custom field payload with automatic field creation
		logDebug(requestId, "Creating CC custom field payload with automatic field creation");
		const { payload: ccCustomFieldPayload, createdFields: createdCustomFields } = await createCcCustomFieldPayloadWithCreation(
			apCustomFieldValues,
			ccCustomFields,
			requestId,
		);

		// Step 8: Create CC custom field payload from AP standard fields with automatic creation
		logDebug(requestId, "Creating CC custom field payload from AP standard fields with automatic creation");
		const { payload: standardFieldsAsCustomFields, createdFields: createdStandardFields } = await createCcStandardFieldPayloadWithCreation(
			apStandardFieldValues,
			ccCustomFields,
			requestId,
		);

		// Step 9: Update CC custom fields list with newly created fields
		ccCustomFields = [...ccCustomFields, ...createdCustomFields, ...createdStandardFields];

		// Step 10: Combine both payloads
		const combinedPayload = [...ccCustomFieldPayload, ...standardFieldsAsCustomFields];

		if (combinedPayload.length === 0) {
			logInfo(requestId, "No custom fields to sync to CC");
		} else {
			// Step 11: Update CC patient with custom fields
			logInfo(requestId, `Updating CC patient ${patientInstance.ccId} with ${combinedPayload.length} custom fields`);
			await patientReq.update(patientInstance.ccId, {
				customFields: combinedPayload,
			} as any); // Type assertion needed - CC API accepts full objects despite TypeScript definition
			logInfo(requestId, `Successfully updated CC patient with ${combinedPayload.length} custom fields`);
		}

		// Step 12: Update local database with fresh data and timestamps
		logDebug(requestId, "Updating local database with fresh AP data and timestamps");
		const db = getDb();
		const now = new Date();

		const updatedPatientResults = await db
			.update(dbSchema.patient)
			.set({
				apData: freshApContactData,
				apUpdatedAt: now,
				updatedAt: now,
			})
			.where(eq(dbSchema.patient.id, patientInstance.id))
			.returning();

		const updatedPatient = updatedPatientResults[0];
		logInfo(requestId, `Successfully completed AP to CC synchronization for patient ${patientInstance.id}`);

		return updatedPatient;
	} catch (error) {
		logError(requestId, "Failed to complete AP to CC custom field synchronization:", error);
		throw error;
	}
}

/**
 * Update CC to AP Custom Fields - Complete Synchronization Flow
 *
 * Implements the complete CC → AP synchronization flow including fresh data fetching,
 * field creation, API updates, and local database updates.
 *
 * @param patientInstance - Local database patient instance with ccId and apId
 * @param requestId - Required request ID for logging and tracing
 * @returns Promise<PatientInstance> - Updated patient instance with fresh data
 *
 * @example
 *
```typescript
 * const updatedPatient = await updateCcToApCustomFields(patient, "req-123");
 * ```
 */
export async function updateCcToApCustomFields(
	patientInstance: PatientInstance,
	requestId: string,
): Promise<PatientInstance> {
	logInfo(requestId, `Starting complete CC to AP custom field synchronization for patient ${patientInstance.id}`);

	try {
		// Step 1: Validate input data
		if (!patientInstance.ccId) {
			throw new Error(`Patient ${patientInstance.id} missing CC ID - cannot fetch fresh CC data`);
		}
		if (!patientInstance.apId) {
			throw new Error(`Patient ${patientInstance.id} missing AP ID - cannot update AP contact`);
		}

		// Step 2: Fetch fresh CC data from API
		logDebug(requestId, `Fetching fresh CC patient data for CC ID: ${patientInstance.ccId}`);
		const freshCcPatientData = await patientReq.get(patientInstance.ccId);
		logDebug(requestId, `Fetched fresh CC patient data with ${freshCcPatientData.customFields?.length || 0} custom field IDs`);

		// Step 3: Check if patient has custom fields
		if (!freshCcPatientData.customFields || freshCcPatientData.customFields.length === 0) {
			logInfo(requestId, `No custom fields found for CC patient ${freshCcPatientData.id}`);

			// Still update local database with fresh CC data
			const db = getDb();
			const now = new Date();
			const updatedPatientResults = await db
				.update(dbSchema.patient)
				.set({
					ccData: freshCcPatientData,
					ccUpdatedAt: now,
					updatedAt: now,
				})
				.where(eq(dbSchema.patient.id, patientInstance.id))
				.returning();

			return updatedPatientResults[0];
		}

		// Step 4: Fetch CC patient custom field data with values
		logDebug(requestId, `Fetching ${freshCcPatientData.customFields.length} CC patient custom fields`);
		const ccPatientCustomFields = await patientReq.customFields(freshCcPatientData.customFields);

		if (!ccPatientCustomFields || ccPatientCustomFields.length === 0) {
			logInfo(requestId, "No custom field data returned from CC");

			// Still update local database with fresh CC data
			const db = getDb();
			const now = new Date();
			const updatedPatientResults = await db
				.update(dbSchema.patient)
				.set({
					ccData: freshCcPatientData,
					ccUpdatedAt: now,
					updatedAt: now,
				})
				.where(eq(dbSchema.patient.id, patientInstance.id))
				.returning();

			return updatedPatientResults[0];
		}

		// Step 5: Extract CC custom field values using enhanced system
		logDebug(requestId, "Extracting CC custom field values");
		const ccCustomFieldValues = extractCcCustomFieldValues(ccPatientCustomFields, requestId);

		// Step 6: Get AP custom field definitions
		logDebug(requestId, "Fetching AP custom field definitions");
		let apCustomFields = await apCustomfield.all();
		logDebug(requestId, `Fetched ${apCustomFields.length} AP custom field definitions`);

		// Step 7: Separate standard field mappings from custom field mappings
		const { standardFieldMappings, customFieldMappings } = separateStandardAndCustomFields(
			ccCustomFieldValues,
			requestId,
		);

		// Step 8: Create AP custom field payload with automatic field creation
		logDebug(requestId, "Creating AP custom field payload with automatic field creation");
		const { payload: customFieldPayload, createdFields: createdApFields } = await createApCustomFieldPayloadWithCreation(
			customFieldMappings,
			apCustomFields,
			requestId,
		);

		// Step 9: Update AP custom fields list with newly created fields
		apCustomFields = [...apCustomFields, ...createdApFields];

		// Step 10: Create AP standard field payload
		logDebug(requestId, "Creating AP standard field payload");
		const standardFieldPayload = createApStandardFieldPayload(standardFieldMappings, requestId);

		// Step 11: Update AP contact with both standard and custom fields
		const hasStandardFields = Object.keys(standardFieldPayload).length > 0;
		const hasCustomFields = customFieldPayload.length > 0;

		if (hasStandardFields || hasCustomFields) {
			const updatePayload = {
				...standardFieldPayload,
				customFields: customFieldPayload,
			};

			logInfo(requestId, `Updating AP contact ${patientInstance.apId} with ${Object.keys(standardFieldPayload).length} standard fields and ${customFieldPayload.length} custom fields`);
			const updatedApContact = await contactReq.update(patientInstance.apId, updatePayload);
			logInfo(requestId, `Successfully updated AP contact with ${Object.keys(standardFieldPayload).length} standard fields and ${customFieldPayload.length} custom fields`);

			// Step 12: Update local database with fresh data and timestamps
			logDebug(requestId, "Updating local database with fresh CC data and updated AP data");
			const db = getDb();
			const now = new Date();

			const updatedPatientResults = await db
				.update(dbSchema.patient)
				.set({
					ccData: freshCcPatientData,
					apData: updatedApContact,
					ccUpdatedAt: now,
					apUpdatedAt: now,
					updatedAt: now,
				})
				.where(eq(dbSchema.patient.id, patientInstance.id))
				.returning();

			const updatedPatient = updatedPatientResults[0];
			logInfo(requestId, `Successfully completed CC to AP synchronization for patient ${patientInstance.id}`);

			return updatedPatient;
		} else {
			logInfo(requestId, "No fields to sync to AP");

			// Still update local database with fresh CC data
			const db = getDb();
			const now = new Date();
			const updatedPatientResults = await db
				.update(dbSchema.patient)
				.set({
					ccData: freshCcPatientData,
					ccUpdatedAt: now,
					updatedAt: now,
				})
				.where(eq(dbSchema.patient.id, patientInstance.id))
				.returning();

			return updatedPatientResults[0];
		}
	} catch (error) {
		logError(requestId, "Failed to complete CC to AP custom field synchronization:", error);
		throw error;
	}
}



/**
 * Separate CC custom field values into standard field mappings and custom field mappings
 *
 * Uses the CC_TO_AP_STANDARD_FIELD_MAPPING to identify which CC custom fields should
 * be mapped to AP standard fields vs actual custom fields.
 *
 * @param ccCustomFieldValues - Extracted CC custom field values
 * @param requestId - Request ID for logging
 * @returns Separated standard and custom field mappings
