/**
 * Comprehensive Patient Custom Field Synchronization Methods
 *
 * This module provides the main entry points for complete custom field synchronization
 * between AutoPatient (AP) and CliniCore (CC) systems using the enhanced bidirectional
 * synchronization system.
 *
 * Features:
 * - Complete AP to CC custom field synchronization with standard field mapping
 * - Complete CC to AP custom field synchronization with standard/custom field separation
 * - Leverages enhanced bidirectional value extraction, conversion, and validation
 * - <PERSON>les standard field mapping issue (CC custom fields → AP standard fields)
 * - Production-ready with comprehensive error handling and logging
 * - TypeScript compliant with strict type safety
 * - Performance optimized following existing codebase patterns
 *
 * Usage:
 * ```typescript
 * import { updateApToCcCustomFields, updateCcToApCustomFields } from '@/processors/bidirectional/patientSyncMethods';
 *
 * // Sync AP data to CC format
 * const ccPayload = await updateApToCcCustomFields(patientInstance, requestId);
 * await patientReq.update(ccPatientId, { customFields: ccPayload });
 *
 * // Sync CC data to AP format
 * const apPayload = await updateCcToApCustomFields(patientInstance, requestId);
 * await contactReq.update(apContactId, { ...apPayload.standardFields, customFields: apPayload.customFields });
 * ```
 */
import { dbSchema, getDb } from "@database";
import type {
	APGetCustomFieldType,
	GetAPContactType,
	GetCCCustomField,
	GetCCPatientCustomField,
	GetCCPatientType,
	PostAPContactType,
	PostCCPatientCustomfield,
} from "@type";
import { apCustomfield, ccCustomfieldReq, patientReq } from "@/apiClient";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import {
	extractApCustomFieldValues,
	extractCcCustomFieldValues,
	extractApStandardFieldValues,
	createApCustomFieldPayload,
	createCcCustomFieldPayload,
	createApStandardFieldPayload,
	type ApCustomFieldValue,
	type CcCustomFieldValue,
} from "./valueHandler";

/**
 * Patient instance type from database schema
 * Represents a patient record with both AP and CC data
 */
export interface PatientInstance {
	id: string;
	apId: string | null;
	ccId: number | null;
	email: string | null;
	phone: string | null;
	apUpdatedAt: Date | null;
	ccUpdatedAt: Date | null;
	apData: GetAPContactType | null;
	ccData: GetCCPatientType | null;
	createdAt: Date;
	updatedAt: Date;
}

/**
 * Result type for CC to AP synchronization
 * Separates standard fields from custom fields for proper AP API usage
 */
export interface CcToApSyncResult {
	standardFields: Partial<PostAPContactType>;
	customFields: { id: string; value: string | number }[];
}

/**
 * Update AP to CC Custom Fields
 *
 * Takes a local database patient instance and creates a comprehensive CC custom field
 * payload for API updates. Handles both standard fields (that are custom fields in CC)
 * and actual custom fields using the enhanced bidirectional synchronization system.
 *
 * @param patientInstance - Local database patient instance with apData
 * @param requestId - Optional request ID for logging (generates UUID if not provided)
 * @returns Promise<PostCCPatientCustomfield[]> - Properly formatted CC custom field payload
 *
 * @example
 * ```typescript
 * const ccPayload = await updateApToCcCustomFields(patient, "req-123");
 * await patientReq.update(patient.ccId, { customFields: ccPayload });
 * ```
 */
export async function updateApToCcCustomFields(
	patientInstance: ,
	requestId?: string,
): Promise<PostCCPatientCustomfield[]> {
	const reqId = requestId || uuidv4();
	
	logInfo(reqId, `Starting comprehensive AP to CC custom field synchronization for patient ${patientInstance.id}`);

	try {
		// Validate input data
		if (!patientInstance.apData) {
			logWarn(reqId, `No AP data found for patient ${patientInstance.id}`);
			return [];
		}

		const apContactData = patientInstance.apData;

		// Step 1: Get AP custom field definitions
		logDebug(reqId, "Fetching AP custom field definitions");
		const apCustomFields = await apCustomfield.all();
		logDebug(reqId, `Fetched ${apCustomFields.length} AP custom field definitions`);

		// Step 2: Extract AP custom field values using enhanced system
		logDebug(reqId, "Extracting AP custom field values");
		const apCustomFieldValues = await extractApCustomFieldValues(
			apContactData,
			apCustomFields,
			reqId,
		);

		// Step 3: Extract AP standard field values for CC custom field mapping
		logDebug(reqId, "Extracting AP standard field values for CC mapping");
		const apStandardFieldValues = extractApStandardFieldValues(apContactData, reqId);

		// Step 4: Get CC custom field definitions
		logDebug(reqId, "Fetching CC custom field definitions");
		const ccCustomFields = await ccCustomfieldReq.all();
		logDebug(reqId, `Fetched ${ccCustomFields.length} CC custom field definitions`);

		// Step 5: Create CC custom field payload using enhanced system
		logDebug(reqId, "Creating CC custom field payload from AP values");
		const ccCustomFieldPayload = createCcCustomFieldPayload(
			apCustomFieldValues,
			ccCustomFields,
			reqId,
		);

		// Step 6: Create CC custom field payload from AP standard fields
		logDebug(reqId, "Creating CC custom field payload from AP standard fields");
		const standardFieldsAsCustomFields = createCcCustomFieldPayloadFromStandardFields(
			apStandardFieldValues,
			ccCustomFields,
			reqId,
		);

		// Step 7: Combine both payloads
		const combinedPayload = [...ccCustomFieldPayload, ...standardFieldsAsCustomFields];

		logInfo(
			reqId,
			`Successfully created CC custom field payload with ${combinedPayload.length} fields (${ccCustomFieldPayload.length} custom + ${standardFieldsAsCustomFields.length} standard)`,
		);

		return combinedPayload;
	} catch (error) {
		logError(reqId, "Failed to create AP to CC custom field payload:", error);
		throw error;
	}
}

/**
 * Update CC to AP Custom Fields
 *
 * Takes a local database patient instance and creates comprehensive AP contact payload
 * for API updates. Separates standard field mappings from custom field mappings and
 * uses the enhanced bidirectional synchronization system.
 *
 * @param patientInstance - Local database patient instance with ccData
 * @param requestId - Optional request ID for logging (generates UUID if not provided)
 * @returns Promise<CcToApSyncResult> - Separated standard fields and custom fields for AP API
 *
 * @example
 * ```typescript
 * const apPayload = await updateCcToApCustomFields(patient, "req-123");
 * await contactReq.update(patient.apId, {
 *   ...apPayload.standardFields,
 *   customFields: apPayload.customFields
 * });
 * ```
 */
export async function updateCcToApCustomFields(
	patientInstance: PatientInstance,
	requestId?: string,
): Promise<CcToApSyncResult> {
	const reqId = requestId || uuidv4();
	
	logInfo(reqId, `Starting comprehensive CC to AP custom field synchronization for patient ${patientInstance.id}`);

	try {
		// Validate input data
		if (!patientInstance.ccData || !patientInstance.ccId) {
			logWarn(reqId, `No CC data or CC ID found for patient ${patientInstance.id}`);
			return { standardFields: {}, customFields: [] };
		}

		const ccPatientData = patientInstance.ccData;

		// Step 1: Check if patient has custom fields
		if (!ccPatientData.customFields || ccPatientData.customFields.length === 0) {
			logInfo(reqId, `No custom fields found for CC patient ${ccPatientData.id}`);
			return { standardFields: {}, customFields: [] };
		}

		// Step 2: Fetch CC patient custom field data with values
		logDebug(reqId, `Fetching ${ccPatientData.customFields.length} CC patient custom fields`);
		const ccPatientCustomFields = await patientReq.customFields(ccPatientData.customFields);

		if (!ccPatientCustomFields || ccPatientCustomFields.length === 0) {
			logInfo(reqId, "No custom field data returned from CC");
			return { standardFields: {}, customFields: [] };
		}

		// Step 3: Extract CC custom field values using enhanced system
		logDebug(reqId, "Extracting CC custom field values");
		const ccCustomFieldValues = extractCcCustomFieldValues(ccPatientCustomFields, reqId);

		// Step 4: Get AP custom field definitions
		logDebug(reqId, "Fetching AP custom field definitions");
		const apCustomFields = await apCustomfield.all();
		logDebug(reqId, `Fetched ${apCustomFields.length} AP custom field definitions`);

		// Step 5: Separate standard field mappings from custom field mappings
		const { standardFieldMappings, customFieldMappings } = separateStandardAndCustomFields(
			ccCustomFieldValues,
			reqId,
		);

		// Step 6: Create AP standard field payload
		logDebug(reqId, "Creating AP standard field payload");
		const standardFieldPayload = createApStandardFieldPayload(standardFieldMappings, reqId);

		// Step 7: Create AP custom field payload
		logDebug(reqId, "Creating AP custom field payload");
		const customFieldPayload = createApCustomFieldPayload(
			customFieldMappings,
			apCustomFields,
			reqId,
		);

		logInfo(
			reqId,
			`Successfully created AP payload with ${Object.keys(standardFieldPayload).length} standard fields and ${customFieldPayload.length} custom fields`,
		);

		return {
			standardFields: standardFieldPayload,
			customFields: customFieldPayload,
		};
	} catch (error) {
		logError(reqId, "Failed to create CC to AP custom field payload:", error);
		throw error;
	}
}

/**
 * Create CC custom field payload from AP standard fields
 *
 * Converts AP standard field values to CC custom field format by finding matching
 * CC custom fields and creating proper PostCCPatientCustomfield entries.
 *
 * @param standardFieldValues - Map of AP standard field names to values
 * @param ccCustomFields - Available CC custom field definitions
 * @param requestId - Request ID for logging
 * @returns Array of CC custom field mappings for standard fields
 */
function createCcCustomFieldPayloadFromStandardFields(
	standardFieldValues: Record<string, string>,
	ccCustomFields: GetCCCustomField[],
	requestId: string,
): PostCCPatientCustomfield[] {
	const payload: PostCCPatientCustomfield[] = [];

	if (Object.keys(standardFieldValues).length === 0) {
		logDebug(requestId, "No AP standard field values to convert to CC custom fields");
		return payload;
	}

	// Standard field mapping variations for CC custom field matching
	const standardFieldMappings: Record<string, string[]> = {
		phone: ["phone-mobile", "phonemobile", "phone mobile", "telefon", "telephone", "mobile"],
		email: ["e-mail", "email", "courriel", "correo electronico"],
		firstName: ["first-name", "firstname", "first name", "vorname", "prenom"],
		lastName: ["last-name", "lastname", "last name", "nachname", "nom"],
		dateOfBirth: ["date-of-birth", "dob", "birth-date", "geburtsdatum"],
		gender: ["geschlecht", "sexe", "genero"],
		address1: ["address", "street", "strasse", "rue", "direccion"],
		city: ["stadt", "ville", "ciudad"],
		state: ["bundesland", "region", "estado"],
		postalCode: ["zip", "plz", "code-postal", "codigo-postal"],
	};

	for (const [apFieldName, fieldValue] of Object.entries(standardFieldValues)) {
		const ccFieldVariations = standardFieldMappings[apFieldName] || [apFieldName];

		// Try to find matching CC custom field
		let matchedCcField: GetCCCustomField | undefined;

		for (const variation of ccFieldVariations) {
			matchedCcField = ccCustomFields.find(
				(ccField) =>
					ccField.name.toLowerCase() === variation.toLowerCase() ||
					ccField.label.toLowerCase() === variation.toLowerCase(),
			);

			if (matchedCcField) {
				logDebug(
					requestId,
					`Matched AP standard field "${apFieldName}" to CC custom field "${matchedCcField.label}" via variation "${variation}"`,
				);
				break;
			}
		}

		if (matchedCcField) {
			const fieldMapping: PostCCPatientCustomfield = {
				field: matchedCcField,
				values: [{ value: fieldValue }],
				patient: null, // Will be set by CC API
			};

			// Handle allowedValues if present
			if (matchedCcField.allowedValues && matchedCcField.allowedValues.length > 0) {
				const allowedValue = matchedCcField.allowedValues.find(
					(v) => v.value?.toLowerCase() === fieldValue.toLowerCase(),
				);
				if (allowedValue) {
					fieldMapping.values = [{ id: allowedValue.id }];
					logDebug(
						requestId,
						`Using allowed value ID ${allowedValue.id} for standard field "${apFieldName}"`,
					);
				}
			}

			payload.push(fieldMapping);
			logDebug(
				requestId,
				`Added standard field mapping: "${apFieldName}" → CC field "${matchedCcField.label}"`,
			);
		} else {
			logWarn(
				requestId,
				`No matching CC custom field found for AP standard field "${apFieldName}" (tried variations: ${ccFieldVariations.join(", ")})`,
			);
		}
	}

	logDebug(
		requestId,
		`Created ${payload.length} CC custom field mappings from ${Object.keys(standardFieldValues).length} AP standard fields`,
	);

	return payload;
}

/**
 * Separate CC custom field values into standard field mappings and custom field mappings
 *
 * Uses the CC_TO_AP_STANDARD_FIELD_MAPPING to identify which CC custom fields should
 * be mapped to AP standard fields vs actual custom fields.
 *
 * @param ccCustomFieldValues - Extracted CC custom field values
 * @param requestId - Request ID for logging
 * @returns Separated standard and custom field mappings
 */
function separateStandardAndCustomFields(
	ccCustomFieldValues: CcCustomFieldValue[],
	requestId: string,
): {
	standardFieldMappings: Record<string, string>;
	customFieldMappings: CcCustomFieldValue[];
} {
	const standardFieldMappings: Record<string, string> = {};
	const customFieldMappings: CcCustomFieldValue[] = [];

	// CC to AP standard field mapping (from existing codebase)
	const CC_TO_AP_STANDARD_FIELD_MAPPING: Record<string, string> = {
		"phone-mobile": "phone",
		phonemobile: "phone",
		"phone mobile": "phone",
		telefon: "phone",
		telephone: "phone",
		mobile: "phone",
		"e-mail": "email",
		email: "email",
		courriel: "email",
		"correo electronico": "email",
		"first-name": "firstName",
		firstname: "firstName",
		"first name": "firstName",
		vorname: "firstName",
		prenom: "firstName",
		"last-name": "lastName",
		lastname: "lastName",
		"last name": "lastName",
		nachname: "lastName",
		nom: "lastName",
		"date-of-birth": "dateOfBirth",
		dob: "dateOfBirth",
		"birth-date": "dateOfBirth",
		geburtsdatum: "dateOfBirth",
		geschlecht: "gender",
		sexe: "gender",
		genero: "gender",
		address: "address1",
		street: "address1",
		strasse: "address1",
		rue: "address1",
		direccion: "address1",
		stadt: "city",
		ville: "city",
		ciudad: "city",
		bundesland: "state",
		region: "state",
		estado: "state",
		zip: "postalCode",
		plz: "postalCode",
		"code-postal": "postalCode",
		"codigo-postal": "postalCode",
	};

	for (const ccValue of ccCustomFieldValues) {
		const fieldName = ccValue.fieldName.toLowerCase();
		const fieldLabel = ccValue.fieldLabel.toLowerCase();

		// Check if this CC custom field should be mapped to an AP standard field
		const apStandardField =
			CC_TO_AP_STANDARD_FIELD_MAPPING[fieldName] ||
			CC_TO_AP_STANDARD_FIELD_MAPPING[fieldLabel];

		if (apStandardField) {
			// This is a standard field mapping
			standardFieldMappings[apStandardField] = ccValue.value;
			logDebug(
				requestId,
				`Mapped CC custom field "${ccValue.fieldLabel}" to AP standard field "${apStandardField}"`,
			);
		} else {
			// This is a custom field mapping
			customFieldMappings.push(ccValue);
			logDebug(
				requestId,
				`Keeping CC custom field "${ccValue.fieldLabel}" as custom field`,
			);
		}
	}

	logDebug(
		requestId,
		`Separated ${ccCustomFieldValues.length} CC fields into ${Object.keys(standardFieldMappings).length} standard mappings and ${customFieldMappings.length} custom mappings`,
	);

	return { standardFieldMappings, customFieldMappings };
}
