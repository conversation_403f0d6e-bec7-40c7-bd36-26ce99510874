# Enhanced Bidirectional Custom Field Synchronization

This document provides an overview of the enhanced bidirectional custom field synchronization system that addresses critical issues with standard field handling and custom field value synchronization between AutoPatient (AP) and CliniCore (CC) systems.

## Issues Addressed

### 1. Standard Field Handling Issue ✅ RESOLVED

**Problem**: In CliniCore (CC), standard contact fields (like email, phone, address, etc.) are treated as custom fields, while in AutoPatient (AP) they are standard contact properties. The system needed to properly distinguish between actual custom fields and these "standard fields treated as custom fields" in CC.

**Solution Implemented**:
- **Enhanced Standard Field Mapping**: Expanded `CC_TO_AP_STANDARD_FIELD_MAPPING` and `AP_TO_CC_STANDARD_FIELD_MAPPING` with comprehensive international variations
- **Comprehensive Field Recognition**: Updated `STANDARD_CONTACT_FIELDS` array with 100+ field name variations in multiple languages
- **Duplicate Prevention**: Improved logic to prevent standard fields from being created as duplicate custom fields during sync
- **Bidirectional Mapping**: Ensured proper mapping between AP standard contact properties and CC custom fields that represent the same data

### 2. Custom Field Value Synchronization ✅ RESOLVED

**Problem**: The system focused on field definition mapping but didn't fully address how to sync the actual custom field VALUES between systems, including handling different data structures.

**Solution Implemented**:
- **Enhanced Value Extraction**: Created `extractApCustomFieldValues()` and `extractCcCustomFieldValues()` functions that properly handle different data structures
- **Robust Value Writing**: Implemented `createApCustomFieldPayload()` and `createCcCustomFieldPayload()` functions for proper value insertion
- **Multi-Value Handling**: Added logic to handle CC's multiple values per field vs AP's single values
- **Special Field Types**: Added support for tags arrays, boolean conversions, and complex data types
- **Value Validation**: Implemented comprehensive validation with `validateCustomFieldValue()` and batch validation

## Key Enhancements

### 1. Comprehensive Standard Field Mapping

```typescript
// Enhanced mapping with 100+ international variations
const CC_TO_AP_STANDARD_FIELD_MAPPING = {
  // Phone variations (20+ variations)
  "phone-mobile": "phone",
  "telefon": "phone",
  "mobile": "phone",
  "numero de telefono": "phone",
  // ... many more

  // Email variations (10+ variations)
  "e-mail": "email",
  "courriel": "email",
  "correo electronico": "email",
  // ... many more

  // Address, name, date variations...
};
```

### 2. Enhanced Value Handling

```typescript
// Extract values with proper structure handling
const apValues = await extractApCustomFieldValues(
  apContactData,
  apCustomFields,
  requestId
);

// Create proper payloads for updates
const ccPayload = createCcCustomFieldPayload(
  apValues,
  targetCcFields,
  requestId
);
```

### 3. Bidirectional Consistency Validation

```typescript
// Test round-trip integrity
const roundTripResult = validateApToCcToApRoundTrip(
  originalValue,
  apField,
  ccField,
  requestId
);

// Comprehensive sync validation
const validationResult = performComprehensiveSyncValidation(
  fieldMappings,
  testValues,
  requestId
);
```

### 4. Enhanced Type Compatibility

```typescript
// Graceful handling of type mismatches
const compatibilityResult = checkFieldTypeCompatibility(
  sourceType,
  targetType,
  fieldName,
  direction,
  requestId
);

// Handles PHONE/telephone, TEXT/textarea mismatches as compatible
```

## Usage Examples

### Basic Value Synchronization

```typescript
import {
  extractApCustomFieldValues,
  createCcCustomFieldPayload,
  validateApToCcCompatibility
} from '@/processors/bidirectional';

// Extract AP custom field values
const apValues = await extractApCustomFieldValues(
  apContactData,
  apCustomFields,
  requestId
);

// Validate compatibility
for (const mapping of fieldMappings) {
  const isCompatible = validateApToCcCompatibility(
    mapping.apField,
    mapping.ccField,
    requestId
  );
}

// Create CC payload
const ccPayload = createCcCustomFieldPayload(
  apValues,
  targetCcFields,
  requestId
);
```

### Standard Field Handling

```typescript
import {
  extractApStandardFieldValues,
  createApStandardFieldPayload
} from '@/processors/bidirectional';

// Extract standard field values for CC sync
const standardValues = extractApStandardFieldValues(
  apContactData,
  requestId
);

// Create AP standard field payload from CC data
const apStandardPayload = createApStandardFieldPayload(
  standardFieldMappings,
  requestId
);
```

### Consistency Validation

```typescript
import {
  performComprehensiveSyncValidation,
  generateStandardTestValues
} from '@/processors/bidirectional';

// Generate test values
const testValues = generateStandardTestValues();

// Validate entire sync process
const validationResult = performComprehensiveSyncValidation(
  fieldMappings,
  testValues,
  requestId
);

console.log(`Validation ${validationResult.isValid ? 'PASSED' : 'FAILED'}`);
console.log(`Critical issues: ${validationResult.summary.criticalIssues}`);
```

## Key Features

### ✅ Standard Field Recognition
- 100+ field name variations in multiple languages
- Prevents duplicate custom field creation
- Proper AP standard ↔ CC custom field mapping

### ✅ Enhanced Value Handling
- Proper extraction from both AP and CC data structures
- Multi-value to single-value conversion
- Special handling for arrays (tags), booleans, dates

### ✅ Type Compatibility
- Graceful handling of PHONE/telephone mismatches
- TEXT/textarea compatibility
- Comprehensive type conversion matrix

### ✅ Bidirectional Consistency
- Round-trip validation (AP→CC→AP, CC→AP→CC)
- Value integrity testing
- Acceptable formatting difference detection

### ✅ Production Ready
- Comprehensive error handling
- Performance optimization
- Detailed logging and monitoring
- Strict TypeScript compliance

## Data Structure Handling

### AutoPatient (AP) Custom Fields
```typescript
// AP stores custom field values as:
contact.customFields: Array<{
  id: string;        // Field definition ID
  value: string;     // Single value
}>
```

### CliniCore (CC) Custom Fields
```typescript
// CC stores custom field values as:
patient.customFields: Array<{
  field: GetCCCustomField;     // Field definition
  values: Array<{             // Multiple values possible
    id?: number;
    value?: string;
    createdAt: string;
    updatedAt: string;
  }>;
}>
```

## Migration Guide

### From Old System
1. Replace direct field mapping calls with enhanced functions
2. Use new value extraction and payload creation functions
3. Add consistency validation for critical sync processes
4. Update field mapping arrays with enhanced variations

### Testing
1. Use `generateStandardTestValues()` for comprehensive testing
2. Run `performComprehensiveSyncValidation()` on field mappings
3. Monitor round-trip validation results
4. Check for acceptable vs critical inconsistencies

## Performance Considerations

- Field definition lookup maps for O(1) performance
- Batch validation for multiple fields
- Optimized value normalization
- Efficient compatibility checking

## Error Handling

- Graceful degradation for incompatible types
- Detailed error messages with context
- Acceptable formatting difference detection
- Comprehensive logging for debugging

This enhanced system provides robust, production-ready bidirectional synchronization with proper handling of both standard field mapping and custom field value synchronization between AP and CC systems.
