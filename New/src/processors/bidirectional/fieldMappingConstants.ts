/**
 * Centralized Field Mapping Constants
 *
 * This module contains all standard field mapping definitions used throughout the
 * bidirectional synchronization system. Centralizing these mappings ensures
 * consistency across all processors and prevents duplication.
 *
 * Features:
 * - CC to AP standard field mapping with comprehensive international variations
 * - AP to CC standard field mapping with field name variations
 * - Standard contact field definitions for field recognition
 * - Proper TypeScript typing for all mapping objects
 *
 * Usage:
 * ```typescript
 * import { CC_TO_AP_STANDARD_FIELD_MAPPING, AP_TO_CC_STANDARD_FIELD_MAPPING } from '@/processors/bidirectional/fieldMappingConstants';
 * 
 * const apField = CC_TO_AP_STANDARD_FIELD_MAPPING[ccFieldName];
 * const ccVariations = AP_TO_CC_STANDARD_FIELD_MAPPING[apFieldName];
 * ```
 */

/**
 * Mapping configuration for CC custom fields to AP standard contact fields
 * Maps CC custom field names/labels to AP standard field names
 *
 * This mapping handles the critical issue where CC stores standard contact fields
 * (like email, phone) as custom fields, while AP treats them as standard properties.
 */
export const CC_TO_AP_STANDARD_FIELD_MAPPING: Record<string, string> = {
	// Phone field variations (20+ international variations)
	"phone-mobile": "phone",
	phonemobile: "phone",
	"phone mobile": "phone",
	"telefon mobil": "phone",
	"telefon-mobil": "phone",
	telefon: "phone",
	telephone: "phone",
	mobile: "phone",
	handy: "phone",
	mobiltelefon: "phone",
	"cell phone": "phone",
	"cell-phone": "phone",
	cellular: "phone",
	"mobile phone": "phone",
	"mobile number": "phone",
	"mobile-number": "phone",
	cell: "phone",
	cellphone: "phone",
	handynummer: "phone",
	"numero de telefono": "phone",
	"numero de celular": "phone",
	"numero movil": "phone",
	"telefono movil": "phone",
	"telefono celular": "phone",

	// Email field variations (10+ international variations)
	"e-mail": "email",
	email: "email",
	courriel: "email",
	"correo electronico": "email",
	"correo electrónico": "email",
	"e-mail address": "email",
	"email address": "email",
	"electronic mail": "email",
	"adresse email": "email",
	"adresse e-mail": "email",
	"direccion de correo": "email",

	// Name field variations
	"first-name": "firstName",
	firstname: "firstName",
	"first name": "firstName",
	vorname: "firstName",
	prenom: "firstName",
	"prénom": "firstName",
	nombre: "firstName",
	"primer nombre": "firstName",

	"last-name": "lastName",
	lastname: "lastName",
	"last name": "lastName",
	nachname: "lastName",
	nom: "lastName",
	apellido: "lastName",
	"apellido paterno": "lastName",
	surname: "lastName",
	"family name": "lastName",

	// Date of birth variations
	"date-of-birth": "dateOfBirth",
	dob: "dateOfBirth",
	"birth-date": "dateOfBirth",
	"birth date": "dateOfBirth",
	geburtsdatum: "dateOfBirth",
	"date de naissance": "dateOfBirth",
	"fecha de nacimiento": "dateOfBirth",
	birthday: "dateOfBirth",
	birthdate: "dateOfBirth",

	// Gender variations
	geschlecht: "gender",
	sexe: "gender",
	genero: "gender",
	género: "gender",
	sex: "gender",
	sexo: "gender",

	// Address variations
	address: "address1",
	street: "address1",
	strasse: "address1",
	rue: "address1",
	direccion: "address1",
	dirección: "address1",
	"street address": "address1",
	"address line 1": "address1",
	"address1": "address1",
	adresse: "address1",

	// City variations
	stadt: "city",
	ville: "city",
	ciudad: "city",
	city: "city",
	locality: "city",

	// State/Region variations
	bundesland: "state",
	region: "state",
	estado: "state",
	state: "state",
	province: "state",
	provincia: "state",

	// Postal code variations
	zip: "postalCode",
	plz: "postalCode",
	"code-postal": "postalCode",
	"code postal": "postalCode",
	"codigo-postal": "postalCode",
	"código postal": "postalCode",
	"postal code": "postalCode",
	zipcode: "postalCode",
	"zip code": "postalCode",
	postcode: "postalCode",
};

/**
 * Mapping configuration for AP standard contact fields to CC custom field variations
 * Maps AP standard field names to arrays of CC custom field name variations to try
 *
 * This is the inverse of CC_TO_AP_STANDARD_FIELD_MAPPING and is used when syncing
 * AP standard fields to CC custom fields.
 */
export const AP_TO_CC_STANDARD_FIELD_MAPPING: Record<string, string[]> = {
	// Phone field variations - comprehensive list from CC_TO_AP mapping
	phone: [
		"phone-mobile",
		"phonemobile",
		"phone mobile",
		"telefon mobil",
		"telefon-mobil",
		"telefon",
		"telephone",
		"mobile",
		"handy",
		"mobiltelefon",
		"cell phone",
		"cell-phone",
		"cellular",
		"mobile phone",
		"mobile number",
		"mobile-number",
		"cell",
		"cellphone",
		"handynummer",
		"numero de telefono",
		"numero de celular",
		"numero movil",
		"telefono movil",
		"telefono celular",
	],

	// Email field variations
	email: [
		"e-mail",
		"email",
		"courriel",
		"correo electronico",
		"correo electrónico",
		"e-mail address",
		"email address",
		"electronic mail",
		"adresse email",
		"adresse e-mail",
		"direccion de correo",
	],

	// First name variations
	firstName: [
		"first-name",
		"firstname",
		"first name",
		"vorname",
		"prenom",
		"prénom",
		"nombre",
		"primer nombre",
	],

	// Last name variations
	lastName: [
		"last-name",
		"lastname",
		"last name",
		"nachname",
		"nom",
		"apellido",
		"apellido paterno",
		"surname",
		"family name",
	],

	// Date of birth variations
	dateOfBirth: [
		"date-of-birth",
		"dob",
		"birth-date",
		"birth date",
		"geburtsdatum",
		"date de naissance",
		"fecha de nacimiento",
		"birthday",
		"birthdate",
	],

	// Gender variations
	gender: [
		"geschlecht",
		"sexe",
		"genero",
		"género",
		"sex",
		"sexo",
	],

	// Address variations
	address1: [
		"address",
		"street",
		"strasse",
		"rue",
		"direccion",
		"dirección",
		"street address",
		"address line 1",
		"address1",
		"adresse",
	],

	// City variations
	city: [
		"stadt",
		"ville",
		"ciudad",
		"city",
		"locality",
	],

	// State variations
	state: [
		"bundesland",
		"region",
		"estado",
		"state",
		"province",
		"provincia",
	],

	// Postal code variations
	postalCode: [
		"zip",
		"plz",
		"code-postal",
		"code postal",
		"codigo-postal",
		"código postal",
		"postal code",
		"zipcode",
		"zip code",
		"postcode",
	],
};

/**
 * Standard contact field definitions for field recognition
 * Used to identify which fields should be treated as standard contact fields
 * rather than custom fields during synchronization.
 */
export const STANDARD_CONTACT_FIELDS: readonly string[] = [
	// Core contact fields
	"email",
	"phone",
	"firstName",
	"lastName",
	"name",

	// Extended contact fields
	"dateOfBirth",
	"gender",
	"address1",
	"city",
	"state",
	"postalCode",
	"country",
	"timezone",
	"companyName",

	// All variations from the mappings above
	...Object.keys(CC_TO_AP_STANDARD_FIELD_MAPPING),
	...Object.values(AP_TO_CC_STANDARD_FIELD_MAPPING).flat(),
] as const;

/**
 * Type definitions for field mapping
 */
export type CCToAPFieldMapping = typeof CC_TO_AP_STANDARD_FIELD_MAPPING;
export type APToCCFieldMapping = typeof AP_TO_CC_STANDARD_FIELD_MAPPING;
export type StandardContactField = typeof STANDARD_CONTACT_FIELDS[number];

/**
 * Helper function to check if a field name is a standard contact field
 */
export function isStandardContactField(fieldName: string): boolean {
	const normalizedFieldName = fieldName.toLowerCase().trim();
	return STANDARD_CONTACT_FIELDS.some(
		standardField => standardField.toLowerCase() === normalizedFieldName
	);
}

/**
 * Helper function to get AP standard field name from CC field name/label
 */
export function getAPStandardFieldName(ccFieldName: string, ccFieldLabel?: string): string | null {
	const normalizedName = ccFieldName.toLowerCase().trim();
	const normalizedLabel = ccFieldLabel?.toLowerCase().trim();

	// Check field name first
	if (CC_TO_AP_STANDARD_FIELD_MAPPING[normalizedName]) {
		return CC_TO_AP_STANDARD_FIELD_MAPPING[normalizedName];
	}

	// Check field label if provided
	if (normalizedLabel && CC_TO_AP_STANDARD_FIELD_MAPPING[normalizedLabel]) {
		return CC_TO_AP_STANDARD_FIELD_MAPPING[normalizedLabel];
	}

	return null;
}

/**
 * Helper function to get CC field variations for an AP standard field
 */
export function getCCFieldVariations(apFieldName: string): string[] {
	return AP_TO_CC_STANDARD_FIELD_MAPPING[apFieldName] || [];
}
