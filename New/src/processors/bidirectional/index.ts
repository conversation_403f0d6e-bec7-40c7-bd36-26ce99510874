/**
 * Bidirectional Custom Field Synchronization Module
 *
 * This module provides a comprehensive bidirectional custom field synchronization system
 * between AutoPatient (AP) and CliniCore (CC) systems. It exports all the necessary
 * functions for field mapping, value conversion, type compatibility checking, and enhanced
 * value handling.
 *
 * Features:
 * - Complete bidirectional field definition mapping
 * - Reversible value conversions with data integrity preservation
 * - Enhanced value reading, writing, and validation
 * - Field type compatibility validation and graceful error handling
 * - Bidirectional consistency testing and round-trip validation
 * - Production-ready performance optimization and logging
 * - Strict TypeScript compliance with comprehensive type safety
 *
 * Usage:
 * ```typescript
 * import {
 *   mapApToCcCustomFields,
 *   mapCcToApCustomFields,
 *   convertApCustomFieldValueToCcCustomField,
 *   convertCcCustomFieldValueToApCustomField,
 *   extractApCustomFieldValues,
 *   extractCcCustomFieldValues,
 *   createApCustomFieldPayload,
 *   createCcCustomFieldPayload,
 *   validateApToCcToApRoundTrip,
 *   performComprehensiveSyncValidation
 * } from '@/processors/bidirectional';
 * ```
 */

// Field Mapping Functions
export {
	mapApToCcCustomFields,
	mapCcToApCustomFields,
} from "./fieldMapper";

// Value Conversion Functions
export {
	convertApCustomFieldValueToCcCustomField,
	convertCcCustomFieldValueToApCustomField,
	convertApBooleanToCc,
	convertCcBooleanToAp,
	convertApMultiValueToCc,
	convertCcMultiValueToAp,
} from "./valueConverter";

// Type Compatibility Functions
export {
	areTypesCompatible,
	validateApToCcCompatibility,
	validateCcToApCompatibility,
	getExpectedCcType,
	getExpectedApType,
	isApBooleanRadioField,
	isCcBooleanField,
	validateFieldDefinition,
	AP_TO_CC_TYPE_MAPPING,
	CC_TO_AP_TYPE_MAPPING,
	TYPE_COMPATIBILITY_MATRIX,
} from "./typeCompatibility";

/**
 * Bidirectional Synchronization Configuration
 *
 * Configuration object for customizing bidirectional synchronization behavior.
 * This can be used to enable/disable specific features or adjust conversion settings.
 */
export interface BidirectionalSyncConfig {
	/** Enable strict type validation (default: false for graceful handling) */
	strictTypeValidation?: boolean;
	
	/** Enable detailed conversion logging (default: true) */
	enableConversionLogging?: boolean;
	
	/** Fallback behavior for incompatible types (default: 'convert_with_warning') */
	incompatibleTypeBehavior?: 'convert_with_warning' | 'skip_field' | 'throw_error';
	
	/** Enable field name normalization (default: true) */
	enableFieldNameNormalization?: boolean;
	
	/** Enable value sanitization (default: true) */
	enableValueSanitization?: boolean;
}

/**
 * Default configuration for bidirectional synchronization
 */
export const DEFAULT_BIDIRECTIONAL_CONFIG: BidirectionalSyncConfig = {
	strictTypeValidation: false,
	enableConversionLogging: true,
	incompatibleTypeBehavior: 'convert_with_warning',
	enableFieldNameNormalization: true,
	enableValueSanitization: true,
};

/**
 * Field Mapping Result Interface
 *
 * Represents the result of a field mapping operation with success/error tracking.
 */
export interface FieldMappingResult<T> {
	/** Successfully mapped fields */
	mappedFields: T[];
	
	/** Number of successfully mapped fields */
	successCount: number;
	
	/** Number of fields that failed to map */
	errorCount: number;
	
	/** Array of error messages for failed mappings */
	errors: string[];
	
	/** Total number of fields processed */
	totalFields: number;
}

/**
 * Value Conversion Result Interface
 *
 * Represents the result of a value conversion operation with metadata.
 */
export interface ValueConversionResult {
	/** Converted value */
	convertedValue: string;
	
	/** Whether the value was actually converted or passed through */
	wasConverted: boolean;
	
	/** Source field type */
	sourceType: string;
	
	/** Target field type */
	targetType: string;
	
	/** Any warnings generated during conversion */
	warnings: string[];
}

/**
 * Synchronization Statistics Interface
 *
 * Provides comprehensive statistics for synchronization operations.
 */
export interface SyncStatistics {
	/** Field mapping statistics */
	fieldMapping: {
		apToCc: FieldMappingResult<any>;
		ccToAp: FieldMappingResult<any>;
	};
	
	/** Value conversion statistics */
	valueConversion: {
		apToCc: number;
		ccToAp: number;
		totalConversions: number;
		conversionErrors: number;
	};
	
	/** Type compatibility statistics */
	typeCompatibility: {
		compatibleFields: number;
		incompatibleFields: number;
		warningsGenerated: number;
	};
	
	/** Overall synchronization timing */
	timing: {
		startTime: Date;
		endTime?: Date;
		durationMs?: number;
	};
}

/**
 * Utility function to create empty synchronization statistics
 *
 * @returns Empty SyncStatistics object
 */
export function createEmptySyncStatistics(): SyncStatistics {
	return {
		fieldMapping: {
			apToCc: {
				mappedFields: [],
				successCount: 0,
				errorCount: 0,
				errors: [],
				totalFields: 0,
			},
			ccToAp: {
				mappedFields: [],
				successCount: 0,
				errorCount: 0,
				errors: [],
				totalFields: 0,
			},
		},
		valueConversion: {
			apToCc: 0,
			ccToAp: 0,
			totalConversions: 0,
			conversionErrors: 0,
		},
		typeCompatibility: {
			compatibleFields: 0,
			incompatibleFields: 0,
			warningsGenerated: 0,
		},
		timing: {
			startTime: new Date(),
		},
	};
}

/**
 * Utility function to finalize synchronization statistics
 *
 * @param stats - SyncStatistics object to finalize
 * @returns Finalized SyncStatistics object with timing information
 */
export function finalizeSyncStatistics(stats: SyncStatistics): SyncStatistics {
	const endTime = new Date();
	const durationMs = endTime.getTime() - stats.timing.startTime.getTime();
	
	return {
		...stats,
		timing: {
			...stats.timing,
			endTime,
			durationMs,
		},
	};
}

// Enhanced Value Handling Functions
export {
	extractApCustomFieldValues,
	extractCcCustomFieldValues,
	extractApStandardFieldValues,
	createApCustomFieldPayload,
	createCcCustomFieldPayload,
	createApStandardFieldPayload,
	validateCustomFieldValue,
	batchValidateCustomFieldValues,
	type ApCustomFieldValue,
	type CcCustomFieldValue,
} from "./valueHandler";

// Bidirectional Consistency Validation Functions
export {
	validateApToCcToApRoundTrip,
	validateCcToApToCcRoundTrip,
	validateFieldMappingConsistency,
	performComprehensiveSyncValidation,
	generateStandardTestValues,
	type RoundTripValidationResult,
	type FieldMappingConsistencyResult,
	type SyncValidationResult,
} from "./consistencyValidator";

/**
 * Re-export commonly used types for convenience
 */
export type {
	APGetCustomFieldType,
	APPostCustomfieldType,
	GetCCCustomField,
	PostCCCustomField,
} from "@type";
