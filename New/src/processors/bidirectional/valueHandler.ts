/**
 * Enhanced Bidirectional Value Handler
 *
 * Provides comprehensive value reading, writing, and conversion for custom field synchronization
 * between AutoPatient (AP) and CliniCore (CC) systems. Handles the different data structures
 * and ensures proper value extraction, validation, and insertion.
 *
 * Features:
 * - Enhanced value extraction from both AP and CC data structures
 * - Proper handling of multi-value fields and single-value conversions
 * - Special handling for array fields (tags) and complex data types
 * - Robust validation and error handling
 * - Performance-optimized value processing
 */

import type {
	APGetCustomFieldType,
	GetAPContactType,
	GetCCCustomField,
	GetCCPatientCustomField,
	PostAPContactType,
	PostCCPatientCustomfield,
} from "@type";
import { logDebug, logError, logWarn } from "@/utils/logger";
import {
	convertApCustomFieldValueToCcCustomField,
	convertCcCustomFieldValueToApCustomField,
} from "./valueConverter";

/**
 * Interface for AP custom field value extraction result
 */
export interface ApCustomFieldValue {
	/** Field ID */
	id: string;
	/** Field name */
	name: string;
	/** Field value */
	value: string;
	/** Field definition */
	fieldDefinition: APGetCustomFieldType;
}

/**
 * Interface for CC custom field value extraction result
 */
export interface CcCustomFieldValue {
	/** Field ID */
	id: number;
	/** Field name */
	name: string;
	/** Field label */
	label: string;
	/** Combined field value */
	value: string;
	/** Field definition */
	fieldDefinition: GetCCCustomField;
	/** Raw values array */
	rawValues: Array<{ id?: number; value?: string }>;
}

/**
 * Enhanced extraction of AP custom field values
 *
 * Reads custom field values from AP contact data and provides comprehensive
 * value extraction with field definition lookup and validation.
 *
 * @param apContactData - AP contact data containing customFields array
 * @param apCustomFields - AP custom field definitions for lookup
 * @param requestId - Request ID for logging
 * @returns Array of extracted AP custom field values
 */
export async function extractApCustomFieldValues(
	apContactData: GetAPContactType,
	apCustomFields: APGetCustomFieldType[],
	requestId: string,
): Promise<ApCustomFieldValue[]> {
	const extractedValues: ApCustomFieldValue[] = [];

	if (!apContactData.customFields || apContactData.customFields.length === 0) {
		logDebug(requestId, "No AP custom fields to extract");
		return extractedValues;
	}

	logDebug(
		requestId,
		`Extracting ${apContactData.customFields.length} AP custom field values`,
	);

	// Create field definition lookup map for performance
	const fieldDefinitionMap = new Map<string, APGetCustomFieldType>();
	for (const field of apCustomFields) {
		fieldDefinitionMap.set(field.id, field);
	}

	let processedCount = 0;
	let validCount = 0;

	for (const customField of apContactData.customFields) {
		processedCount++;

		// Find field definition
		const fieldDefinition = fieldDefinitionMap.get(customField.id);
		if (!fieldDefinition) {
			logDebug(
				requestId,
				`No field definition found for AP custom field ID: ${customField.id}`,
			);
			continue;
		}

		// Validate and normalize value
		const normalizedValue = normalizeFieldValue(customField.value);
		if (!normalizedValue) {
			logDebug(
				requestId,
				`Skipping empty AP custom field: ${fieldDefinition.name}`,
			);
			continue;
		}

		extractedValues.push({
			id: customField.id,
			name: fieldDefinition.name,
			value: normalizedValue,
			fieldDefinition,
		});

		validCount++;

		logDebug(
			requestId,
			`Extracted AP field "${fieldDefinition.name}" = "${normalizedValue.substring(0, 50)}${normalizedValue.length > 50 ? "..." : ""}"`,
		);
	}

	logDebug(
		requestId,
		`AP value extraction completed: ${validCount}/${processedCount} valid values`,
	);

	return extractedValues;
}

/**
 * Enhanced extraction of CC custom field values
 *
 * Reads custom field values from CC patient data and provides comprehensive
 * value extraction with proper multi-value handling and field definition lookup.
 *
 * @param ccPatientCustomFields - CC patient custom field data
 * @param requestId - Request ID for logging
 * @returns Array of extracted CC custom field values
 */
export function extractCcCustomFieldValues(
	ccPatientCustomFields: GetCCPatientCustomField[],
	requestId: string,
): CcCustomFieldValue[] {
	const extractedValues: CcCustomFieldValue[] = [];

	if (!ccPatientCustomFields || ccPatientCustomFields.length === 0) {
		logDebug(requestId, "No CC custom fields to extract");
		return extractedValues;
	}

	logDebug(
		requestId,
		`Extracting ${ccPatientCustomFields.length} CC custom field values`,
	);

	let processedCount = 0;
	let validCount = 0;

	for (const ccCustomField of ccPatientCustomFields) {
		processedCount++;

		// Extract and combine values
		const combinedValue = extractAndCombineCcValues(ccCustomField);
		if (!combinedValue) {
			logDebug(
				requestId,
				`Skipping empty CC custom field: ${ccCustomField.field.name}`,
			);
			continue;
		}

		extractedValues.push({
			id: ccCustomField.field.id,
			name: ccCustomField.field.name,
			label: ccCustomField.field.label,
			value: combinedValue,
			fieldDefinition: ccCustomField.field,
			rawValues: ccCustomField.values || [],
		});

		validCount++;

		logDebug(
			requestId,
			`Extracted CC field "${ccCustomField.field.name}" = "${combinedValue.substring(0, 50)}${combinedValue.length > 50 ? "..." : ""}"`,
		);
	}

	logDebug(
		requestId,
		`CC value extraction completed: ${validCount}/${processedCount} valid values`,
	);

	return extractedValues;
}

/**
 * Extract and combine multiple values from CC custom field
 *
 * Handles different field types and provides appropriate value combination strategies.
 * Multi-value fields are combined with appropriate separators based on field type.
 *
 * @param ccCustomField - CC custom field data with values array
 * @returns Combined value string or empty string if no valid values
 */
function extractAndCombineCcValues(ccCustomField: GetCCPatientCustomField): string {
	if (!ccCustomField.values || ccCustomField.values.length === 0) {
		return "";
	}

	// Filter out null/empty values
	const validValues = ccCustomField.values
		.map((v) => v.value)
		.filter((v): v is string => v != null && v.trim() !== "");

	if (validValues.length === 0) {
		return "";
	}

	// For single value, return as-is
	if (validValues.length === 1) {
		return validValues[0].trim();
	}

	// For multiple values, combine based on field type
	const fieldType = ccCustomField.field.type.toLowerCase();

	if (fieldType.includes("multiselect") || fieldType.includes("checkbox")) {
		// Multi-select fields: comma-separated
		return validValues.join(", ");
	} else if (fieldType.includes("text") || fieldType.includes("textarea")) {
		// Text fields: newline-separated for readability
		return validValues.join("\n");
	} else {
		// Default: comma-separated
		return validValues.join(", ");
	}
}

/**
 * Normalize field value for consistent processing
 *
 * Handles different value types and ensures consistent string output.
 * Trims whitespace and handles null/undefined values.
 *
 * @param value - Raw field value (string, number, or other)
 * @returns Normalized string value or empty string if invalid
 */
function normalizeFieldValue(value: unknown): string {
	if (value == null) {
		return "";
	}

	const stringValue = String(value).trim();
	return stringValue === "null" || stringValue === "undefined" ? "" : stringValue;
}

/**
 * Extract AP standard field values for CC custom field mapping
 *
 * Extracts standard contact field values from AP contact data and prepares them
 * for synchronization to CC custom fields. Handles special field types like tags arrays.
 *
 * @param apContactData - AP contact data
 * @param requestId - Request ID for logging
 * @returns Map of field names to values for CC custom field sync
 */
export function extractApStandardFieldValues(
	apContactData: GetAPContactType,
	requestId: string,
): Record<string, string> {
	const standardValues: Record<string, string> = {};

	logDebug(requestId, "Extracting AP standard field values for CC sync");

	// Extract standard contact fields
	const standardFields = [
		"email", "phone", "name", "firstName", "lastName", "timezone",
		"companyName", "website", "address1", "city", "state", "country",
		"postalCode", "dateOfBirth", "ssn", "gender", "type", "source"
	];

	for (const fieldName of standardFields) {
		const value = apContactData[fieldName as keyof GetAPContactType];
		if (value != null) {
			const normalizedValue = normalizeFieldValue(value);
			if (normalizedValue) {
				standardValues[fieldName] = normalizedValue;
				logDebug(
					requestId,
					`Extracted standard field "${fieldName}" = "${normalizedValue.substring(0, 50)}${normalizedValue.length > 50 ? "..." : ""}"`,
				);
			}
		}
	}

	// Special handling for tags array
	if (apContactData.tags && Array.isArray(apContactData.tags) && apContactData.tags.length > 0) {
		const tagsValue = apContactData.tags.join(", ");
		standardValues.tags = tagsValue;
		logDebug(requestId, `Extracted tags: "${tagsValue}"`);
	}

	logDebug(requestId, `Extracted ${Object.keys(standardValues).length} standard field values`);
	return standardValues;
}

/**
 * Create AP custom field update payload
 *
 * Converts extracted values to AP custom field format for contact updates.
 * Handles value conversion and validation.
 *
 * @param extractedValues - Extracted custom field values
 * @param targetApFields - Target AP custom field definitions
 * @param requestId - Request ID for logging
 * @returns Array of AP custom field updates
 */
export function createApCustomFieldPayload(
	extractedValues: CcCustomFieldValue[],
	targetApFields: APGetCustomFieldType[],
	requestId: string,
): Array<{ id: string; value: string }> {
	const payload: Array<{ id: string; value: string }> = [];

	logDebug(
		requestId,
		`Creating AP custom field payload for ${extractedValues.length} values`,
	);

	for (const extractedValue of extractedValues) {
		// Find matching AP field (this should be done by the caller, but we validate here)
		const apField = targetApFields.find(
			(f) => f.name.toLowerCase() === extractedValue.name.toLowerCase(),
		);

		if (!apField) {
			logWarn(
				requestId,
				`No matching AP field found for CC field: ${extractedValue.name}`,
			);
			continue;
		}

		// Convert value if needed
		const convertedValue = convertCcCustomFieldValueToApCustomField(
			extractedValue.value,
			extractedValue.fieldDefinition,
			apField,
			requestId,
		);

		payload.push({
			id: apField.id,
			value: convertedValue,
		});

		logDebug(
			requestId,
			`Added to AP payload: ${apField.name} = "${convertedValue.substring(0, 50)}${convertedValue.length > 50 ? "..." : ""}"`,
		);
	}

	logDebug(requestId, `AP payload created with ${payload.length} fields`);
	return payload;
}

/**
 * Create AP standard field update payload
 *
 * Converts CC custom field values to AP standard field format for contact updates.
 * Handles special field types like tags arrays and proper type conversion.
 *
 * @param standardFieldMappings - Map of AP standard field names to values
 * @param requestId - Request ID for logging
 * @returns Partial AP contact update payload
 */
export function createApStandardFieldPayload(
	standardFieldMappings: Record<string, string>,
	requestId: string,
): Partial<PostAPContactType> {
	const payload: Partial<PostAPContactType> = {};

	logDebug(
		requestId,
		`Creating AP standard field payload for ${Object.keys(standardFieldMappings).length} fields`,
	);

	for (const [fieldName, value] of Object.entries(standardFieldMappings)) {
		try {
			// Handle special field types
			if (fieldName === "tags") {
				// Convert comma-separated string back to array
				const tagsArray = value.split(",").map(tag => tag.trim()).filter(tag => tag.length > 0);
				payload.tags = tagsArray;
				logDebug(requestId, `Set tags array: [${tagsArray.join(", ")}]`);
			} else if (fieldName === "dnd") {
				// Convert string to boolean
				payload.dnd = value.toLowerCase() === "true" || value === "1";
				logDebug(requestId, `Set dnd: ${payload.dnd}`);
			} else {
				// Standard string fields
				(payload as any)[fieldName] = value;
				logDebug(
					requestId,
					`Set ${fieldName}: "${value.substring(0, 50)}${value.length > 50 ? "..." : ""}"`,
				);
			}
		} catch (error) {
			logWarn(
				requestId,
				`Failed to set standard field "${fieldName}": ${error}`,
			);
		}
	}

	logDebug(requestId, `AP standard field payload created with ${Object.keys(payload).length} fields`);
	return payload;
}

/**
 * Create CC custom field update payload
 *
 * Converts extracted values to CC custom field format for patient updates.
 * Handles value conversion, validation, and allowed values matching.
 *
 * @param extractedValues - Extracted custom field values
 * @param targetCcFields - Target CC custom field definitions
 * @param requestId - Request ID for logging
 * @returns Array of CC custom field updates
 */
export function createCcCustomFieldPayload(
	extractedValues: ApCustomFieldValue[],
	targetCcFields: GetCCCustomField[],
	requestId: string,
): PostCCPatientCustomfield[] {
	const payload: PostCCPatientCustomfield[] = [];

	logDebug(
		requestId,
		`Creating CC custom field payload for ${extractedValues.length} values`,
	);

	for (const extractedValue of extractedValues) {
		// Find matching CC field (this should be done by the caller, but we validate here)
		const ccField = targetCcFields.find(
			(f) =>
				f.name.toLowerCase() === extractedValue.name.toLowerCase() ||
				f.label.toLowerCase() === extractedValue.name.toLowerCase(),
		);

		if (!ccField) {
			logWarn(
				requestId,
				`No matching CC field found for AP field: ${extractedValue.name}`,
			);
			continue;
		}

		// Convert value if needed
		const convertedValue = convertApCustomFieldValueToCcCustomField(
			extractedValue.value,
			extractedValue.fieldDefinition,
			ccField,
			requestId,
		);

		// Create field mapping
		const fieldMapping: PostCCPatientCustomfield = {
			field: ccField,
			values: [{ value: convertedValue }],
			patient: null, // Will be set by CC API
		};

		// Handle allowed values if present
		if (ccField.allowedValues && ccField.allowedValues.length > 0) {
			const allowedValue = ccField.allowedValues.find(
				(v) => v.value === convertedValue,
			);
			if (allowedValue) {
				fieldMapping.values = [{ id: allowedValue.id }];
				logDebug(
					requestId,
					`Using allowed value ID ${allowedValue.id} for field "${extractedValue.name}"`,
				);
			}
		}

		payload.push(fieldMapping);

		logDebug(
			requestId,
			`Added to CC payload: ${ccField.name} = "${convertedValue.substring(0, 50)}${convertedValue.length > 50 ? "..." : ""}"`,
		);
	}

	logDebug(requestId, `CC payload created with ${payload.length} fields`);
	return payload;
}

/**
 * Validate custom field value before processing
 *
 * Performs comprehensive validation of custom field values to ensure they meet
 * system requirements and field type constraints.
 *
 * @param value - Field value to validate
 * @param fieldDefinition - Field definition for validation rules
 * @param requestId - Request ID for logging
 * @returns Validation result with success flag and error message
 */
export function validateCustomFieldValue(
	value: string,
	fieldDefinition: APGetCustomFieldType | GetCCCustomField,
	requestId: string,
): { isValid: boolean; error?: string; normalizedValue?: string } {
	try {
		// Basic validation
		if (!value || value.trim().length === 0) {
			return { isValid: false, error: "Empty value" };
		}

		const normalizedValue = normalizeFieldValue(value);
		if (!normalizedValue) {
			return { isValid: false, error: "Invalid value after normalization" };
		}

		// Type-specific validation
		const fieldType = "dataType" in fieldDefinition
			? fieldDefinition.dataType
			: fieldDefinition.type;

		switch (fieldType.toLowerCase()) {
			case "email":
				if (!isValidEmail(normalizedValue)) {
					return { isValid: false, error: "Invalid email format" };
				}
				break;

			case "phone":
			case "telephone":
				if (!isValidPhone(normalizedValue)) {
					logWarn(requestId, `Phone validation warning for value: ${normalizedValue}`);
					// Don't fail for phone validation, just warn
				}
				break;

			case "number":
				if (!isValidNumber(normalizedValue)) {
					return { isValid: false, error: "Invalid number format" };
				}
				break;

			case "date":
				if (!isValidDate(normalizedValue)) {
					return { isValid: false, error: "Invalid date format" };
				}
				break;

			case "boolean":
				if (!isValidBoolean(normalizedValue)) {
					return { isValid: false, error: "Invalid boolean value" };
				}
				break;
		}

		// Length validation (reasonable limits)
		if (normalizedValue.length > 10000) {
			return { isValid: false, error: "Value too long (max 10000 characters)" };
		}

		return { isValid: true, normalizedValue };

	} catch (error) {
		logError(requestId, `Validation error for field value:`, error);
		return { isValid: false, error: `Validation exception: ${error}` };
	}
}

/**
 * Basic email validation
 */
function isValidEmail(value: string): boolean {
	const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
	return emailRegex.test(value);
}

/**
 * Basic phone validation (allows various formats)
 */
function isValidPhone(value: string): boolean {
	// Remove all non-digit characters for validation
	const digitsOnly = value.replace(/\D/g, "");
	// Allow 7-15 digits (covers most international formats)
	return digitsOnly.length >= 7 && digitsOnly.length <= 15;
}

/**
 * Basic number validation
 */
function isValidNumber(value: string): boolean {
	return !isNaN(Number(value)) && isFinite(Number(value));
}

/**
 * Basic date validation (accepts various formats)
 */
function isValidDate(value: string): boolean {
	const date = new Date(value);
	return !isNaN(date.getTime());
}

/**
 * Basic boolean validation
 */
function isValidBoolean(value: string): boolean {
	const normalizedValue = value.toLowerCase().trim();
	return ["true", "false", "yes", "no", "1", "0"].includes(normalizedValue);
}

/**
 * Batch validate custom field values
 *
 * Validates multiple custom field values and returns results with detailed error information.
 *
 * @param values - Array of values to validate
 * @param fieldDefinitions - Array of field definitions
 * @param requestId - Request ID for logging
 * @returns Validation results with valid and invalid value arrays
 */
export function batchValidateCustomFieldValues(
	values: Array<{ value: string; fieldName: string }>,
	fieldDefinitions: Array<APGetCustomFieldType | GetCCCustomField>,
	requestId: string,
): {
	validValues: Array<{ value: string; fieldName: string; normalizedValue: string }>;
	invalidValues: Array<{ value: string; fieldName: string; error: string }>;
} {
	const validValues: Array<{ value: string; fieldName: string; normalizedValue: string }> = [];
	const invalidValues: Array<{ value: string; fieldName: string; error: string }> = [];

	logDebug(requestId, `Batch validating ${values.length} custom field values`);

	for (const { value, fieldName } of values) {
		// Find field definition
		const fieldDefinition = fieldDefinitions.find(
			(def) => {
				const defName = "name" in def ? def.name : def.label;
				return defName.toLowerCase() === fieldName.toLowerCase();
			}
		);

		if (!fieldDefinition) {
			invalidValues.push({
				value,
				fieldName,
				error: "Field definition not found"
			});
			continue;
		}

		// Validate value
		const validation = validateCustomFieldValue(value, fieldDefinition, requestId);
		if (validation.isValid && validation.normalizedValue) {
			validValues.push({
				value,
				fieldName,
				normalizedValue: validation.normalizedValue
			});
		} else {
			invalidValues.push({
				value,
				fieldName,
				error: validation.error || "Unknown validation error"
			});
		}
	}

	logDebug(
		requestId,
		`Batch validation completed: ${validValues.length} valid, ${invalidValues.length} invalid`
	);

	if (invalidValues.length > 0) {
		logWarn(
			requestId,
			`Validation failures: ${invalidValues.map(iv => `${iv.fieldName}: ${iv.error}`).join(", ")}`
		);
	}

	return { validValues, invalidValues };
}
