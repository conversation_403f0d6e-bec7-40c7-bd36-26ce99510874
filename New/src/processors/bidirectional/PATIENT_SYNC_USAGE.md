# Comprehensive Patient Custom Field Synchronization

This document provides usage examples and best practices for the comprehensive patient custom field synchronization methods that serve as the main entry points for bidirectional custom field sync.

## Overview

The `patientSyncMethods.ts` module provides two main functions that handle complete custom field synchronization:

1. **`updateApToCc<PERSON>ustomFields`** - Converts AP patient data to CC custom field format
2. **`updateCcToApCustomFields`** - Converts CC patient data to AP contact format

Both methods leverage the enhanced bidirectional synchronization system and handle the critical standard field mapping issue where CC stores standard contact fields as custom fields.

## Key Features

- ✅ **Complete Data Extraction**: Uses enhanced value extraction functions
- ✅ **Bidirectional Conversion**: Proper value conversion with type compatibility
- ✅ **Standard Field Mapping**: Handles CC custom fields ↔ AP standard fields
- ✅ **Error Handling**: Comprehensive error handling with detailed logging
- ✅ **Type Safety**: Full TypeScript compliance with strict typing
- ✅ **Performance Optimized**: Follows existing codebase patterns
- ✅ **Production Ready**: Includes request ID tracking and monitoring

## Usage Examples

### Basic AP to CC Synchronization

```typescript
import { updateApToCcCustomFields } from '@/processors/bidirectional';
import { patientReq } from '@/apiClient';

async function syncApPatientToCC(patientInstance: PatientInstance, requestId: string) {
  try {
    // Create CC custom field payload from AP data
    const ccCustomFieldPayload = await updateApToCcCustomFields(patientInstance, requestId);
    
    if (ccCustomFieldPayload.length > 0 && patientInstance.ccId) {
      // Update CC patient with custom fields
      await patientReq.update(patientInstance.ccId, {
        customFields: ccCustomFieldPayload
      });
      
      console.log(`Successfully synced ${ccCustomFieldPayload.length} fields to CC patient ${patientInstance.ccId}`);
    }
  } catch (error) {
    console.error('Failed to sync AP to CC:', error);
  }
}
```

### Basic CC to AP Synchronization

```typescript
import { updateCcToApCustomFields } from '@/processors/bidirectional';
import { contactReq } from '@/apiClient';

async function syncCcPatientToAP(patientInstance: PatientInstance, requestId: string) {
  try {
    // Create AP contact payload from CC data
    const apPayload = await updateCcToApCustomFields(patientInstance, requestId);
    
    if (patientInstance.apId && (Object.keys(apPayload.standardFields).length > 0 || apPayload.customFields.length > 0)) {
      // Update AP contact with both standard and custom fields
      await contactReq.update(patientInstance.apId, {
        ...apPayload.standardFields,
        customFields: apPayload.customFields
      });
      
      console.log(`Successfully synced ${Object.keys(apPayload.standardFields).length} standard fields and ${apPayload.customFields.length} custom fields to AP contact ${patientInstance.apId}`);
    }
  } catch (error) {
    console.error('Failed to sync CC to AP:', error);
  }
}
```

### Complete Bidirectional Sync Workflow

```typescript
import { updateApToCcCustomFields, updateCcToApCustomFields } from '@/processors/bidirectional';
import { patientReq, contactReq } from '@/apiClient';
import { getDb, dbSchema } from '@database';
import { eq } from 'drizzle-orm';

async function performBidirectionalSync(patientId: string, requestId: string) {
  const db = getDb();
  
  try {
    // Get patient instance from database
    const patientResults = await db
      .select()
      .from(dbSchema.patient)
      .where(eq(dbSchema.patient.id, patientId));
    
    if (patientResults.length === 0) {
      throw new Error(`Patient not found: ${patientId}`);
    }
    
    const patientInstance = patientResults[0];
    
    // Sync AP data to CC if both exist
    if (patientInstance.apData && patientInstance.ccId) {
      console.log('Syncing AP data to CC...');
      const ccPayload = await updateApToCcCustomFields(patientInstance, requestId);
      
      if (ccPayload.length > 0) {
        await patientReq.update(patientInstance.ccId, {
          customFields: ccPayload
        });
        console.log(`✅ Synced ${ccPayload.length} fields to CC`);
      }
    }
    
    // Sync CC data to AP if both exist
    if (patientInstance.ccData && patientInstance.apId) {
      console.log('Syncing CC data to AP...');
      const apPayload = await updateCcToApCustomFields(patientInstance, requestId);
      
      if (Object.keys(apPayload.standardFields).length > 0 || apPayload.customFields.length > 0) {
        await contactReq.update(patientInstance.apId, {
          ...apPayload.standardFields,
          customFields: apPayload.customFields
        });
        console.log(`✅ Synced ${Object.keys(apPayload.standardFields).length} standard + ${apPayload.customFields.length} custom fields to AP`);
      }
    }
    
    console.log('Bidirectional sync completed successfully');
  } catch (error) {
    console.error('Bidirectional sync failed:', error);
    throw error;
  }
}
```

### Webhook Integration Example

```typescript
import { updateApToCcCustomFields } from '@/processors/bidirectional';
import { processAPContactWebhook } from '@/processors/ap/contactProcessor';

// In your AP webhook handler
export async function handleAPContactUpdate(webhookPayload: APContactWebhookPayload, requestId: string) {
  try {
    // Process the webhook and get/update patient instance
    const patientInstance = await processAPContactWebhook(webhookPayload, requestId);
    
    // Sync custom fields to CC if CC patient exists
    if (patientInstance.ccId) {
      const ccPayload = await updateApToCcCustomFields(patientInstance, requestId);
      
      if (ccPayload.length > 0) {
        await patientReq.update(patientInstance.ccId, {
          customFields: ccPayload
        });
        
        logInfo(requestId, `Webhook sync: Updated ${ccPayload.length} custom fields in CC patient ${patientInstance.ccId}`);
      }
    }
  } catch (error) {
    logError(requestId, 'Failed to sync AP webhook data to CC:', error);
  }
}
```

## Error Handling Best Practices

```typescript
import { updateApToCcCustomFields } from '@/processors/bidirectional';
import { logError, logWarn } from '@/utils/logger';

async function robustSyncExample(patientInstance: PatientInstance, requestId: string) {
  try {
    // Validate patient instance
    if (!patientInstance.apData) {
      logWarn(requestId, `No AP data available for patient ${patientInstance.id}`);
      return;
    }
    
    if (!patientInstance.ccId) {
      logWarn(requestId, `No CC ID available for patient ${patientInstance.id}`);
      return;
    }
    
    // Perform sync with comprehensive error handling
    const ccPayload = await updateApToCcCustomFields(patientInstance, requestId);
    
    if (ccPayload.length === 0) {
      logWarn(requestId, `No custom fields to sync for patient ${patientInstance.id}`);
      return;
    }
    
    // Update with retry logic
    let retryCount = 0;
    const maxRetries = 3;
    
    while (retryCount < maxRetries) {
      try {
        await patientReq.update(patientInstance.ccId, {
          customFields: ccPayload
        });
        break; // Success, exit retry loop
      } catch (updateError) {
        retryCount++;
        if (retryCount >= maxRetries) {
          throw updateError;
        }
        logWarn(requestId, `Retry ${retryCount}/${maxRetries} for CC update`);
        await new Promise(resolve => setTimeout(resolve, 1000 * retryCount)); // Exponential backoff
      }
    }
    
  } catch (error) {
    logError(requestId, `Robust sync failed for patient ${patientInstance.id}:`, error);
    // Don't re-throw if this is part of a larger process
    // throw error; // Uncomment if you want to propagate the error
  }
}
```

## Performance Considerations

1. **Batch Processing**: When syncing multiple patients, consider batching API calls
2. **Caching**: The methods automatically cache field definitions to reduce API calls
3. **Request ID**: Always provide request IDs for proper logging and debugging
4. **Error Recovery**: Implement retry logic for transient API failures

## Type Safety

Both methods are fully typed and will provide compile-time safety:

```typescript
// TypeScript will enforce correct usage
const ccPayload: PostCCPatientCustomfield[] = await updateApToCcCustomFields(patient, requestId);
const apPayload: CcToApSyncResult = await updateCcToApCustomFields(patient, requestId);

// Access with full type safety
apPayload.standardFields.email; // string | undefined
apPayload.customFields[0].id;   // string
apPayload.customFields[0].value; // string | number
```

## Monitoring and Logging

All methods include comprehensive logging at DEBUG, INFO, WARN, and ERROR levels:

- **DEBUG**: Detailed step-by-step processing information
- **INFO**: High-level sync progress and results
- **WARN**: Non-fatal issues like missing data or field mismatches
- **ERROR**: Critical failures that prevent sync completion

Use appropriate log levels in production to balance observability with performance.
