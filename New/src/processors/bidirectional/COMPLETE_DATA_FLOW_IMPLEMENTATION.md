# Complete Data Flow Implementation - Patient Custom Field Synchronization

This document verifies that the comprehensive patient custom field synchronization methods now implement the correct data flow patterns as specified.

## ✅ **AP → CC Synchronization Flow Implementation**

### Method: `updateApToCcCustomFields(patientInstance, requestId)`

**✅ Step 1: Input Validation**
- Takes local database patient instance with `apId` and `ccId`
- Validates required IDs are present
- Throws descriptive errors for missing data

**✅ Step 2: Fetch Fresh AP Data**
```typescript
const freshApContactData = await contactReq.get(patientInstance.apId);
```
- Calls AP API to get latest contact data including custom field values
- Does NOT rely on cached `apData` from database

**✅ Step 3: Fetch AP Field Definitions**
```typescript
const apCustomFields = await apCustomfield.all();
```
- Gets all AP custom field definitions for mapping

**✅ Step 4: Extract and Convert**
```typescript
const apCustomFieldValues = await extractApCustomFieldValues(freshApContactData, apCustomFields, requestId);
const apStandardFieldValues = extractApStandardFieldValues(freshApContactData, requestId);
```
- Uses enhanced bidirectional system to extract values
- Handles both custom fields and standard fields

**✅ Step 5: Handle Missing CC Fields**
```typescript
const { payload: ccCustomFieldPayload, createdFields: createdCustomFields } = await createCcCustomFieldPayloadWithCreation(...);
const { payload: standardFieldsAsCustomFields, createdFields: createdStandardFields } = await createCcStandardFieldPayloadWithCreation(...);
```
- Automatically creates missing CC custom fields using `ccCustomfieldReq.create()`
- Handles both custom field mappings and standard field mappings
- Graceful error handling for field creation failures

**✅ Step 6: Update CC Patient**
```typescript
await patientReq.update(patientInstance.ccId, {
    customFields: combinedPayload,
} as any);
```
- Sends converted custom field payload to CC
- Uses type assertion for API compatibility

**✅ Step 7: Update Local Database**
```typescript
const updatedPatientResults = await db
    .update(dbSchema.patient)
    .set({
        apData: freshApContactData,
        apUpdatedAt: now,
        updatedAt: now,
    })
    .where(eq(dbSchema.patient.id, patientInstance.id))
    .returning();
```
- Stores fresh AP data in local database
- Updates timestamps properly
- Returns updated patient instance

---

## ✅ **CC → AP Synchronization Flow Implementation**

### Method: `updateCcToApCustomFields(patientInstance, requestId)`

**✅ Step 1: Input Validation**
- Takes local database patient instance with `ccId` and `apId`
- Validates required IDs are present
- Throws descriptive errors for missing data

**✅ Step 2: Fetch Fresh CC Data**
```typescript
const freshCcPatientData = await patientReq.get(patientInstance.ccId);
```
- Calls CC API to get latest patient data
- Does NOT rely on cached `ccData` from database

**✅ Step 3: Fetch CC Custom Field Data**
```typescript
const ccPatientCustomFields = await patientReq.customFields(freshCcPatientData.customFields);
```
- Gets CC patient custom field values (not just IDs)
- Handles cases where no custom fields exist

**✅ Step 4: Extract and Convert**
```typescript
const ccCustomFieldValues = extractCcCustomFieldValues(ccPatientCustomFields, requestId);
```
- Uses enhanced bidirectional system to extract CC values

**✅ Step 5: Separate Standard vs Custom**
```typescript
const { standardFieldMappings, customFieldMappings } = separateStandardAndCustomFields(ccCustomFieldValues, requestId);
```
- Distinguishes CC custom fields that map to AP standard fields
- Uses centralized `CC_TO_AP_STANDARD_FIELD_MAPPING`

**✅ Step 6: Handle Missing AP Fields**
```typescript
const { payload: customFieldPayload, createdFields: createdApFields } = await createApCustomFieldPayloadWithCreation(customFieldMappings, apCustomFields, requestId);
```
- Automatically creates missing AP custom fields using `apCustomfield.create()`
- Graceful error handling for field creation failures

**✅ Step 7: Update AP Contact**
```typescript
const updatePayload = {
    ...standardFieldPayload,
    customFields: customFieldPayload,
};
const updatedApContact = await contactReq.update(patientInstance.apId, updatePayload);
```
- Sends both standard fields and custom fields to AP
- Proper payload structure for AP API

**✅ Step 8: Update Local Database**
```typescript
const updatedPatientResults = await db
    .update(dbSchema.patient)
    .set({
        ccData: freshCcPatientData,
        apData: updatedApContact,
        ccUpdatedAt: now,
        apUpdatedAt: now,
        updatedAt: now,
    })
    .where(eq(dbSchema.patient.id, patientInstance.id))
    .returning();
```
- Stores fresh CC data and updated AP data
- Updates timestamps for both systems
- Returns updated patient instance

---

## ✅ **Key Requirements Met**

### **Fresh Data Fetching**
- ✅ Both methods fetch fresh data from APIs, not cached data
- ✅ AP method: `contactReq.get(apId)` for latest contact data
- ✅ CC method: `patientReq.get(ccId)` for latest patient data

### **Automatic Field Creation**
- ✅ Missing CC fields created automatically with `ccCustomfieldReq.create()`
- ✅ Missing AP fields created automatically with `apCustomfield.create()`
- ✅ Graceful error handling allows partial success
- ✅ Newly created fields are added to field definition lists

### **Local Database Updates**
- ✅ Fresh API data stored in local database
- ✅ Proper timestamp management (`apUpdatedAt`, `ccUpdatedAt`, `updatedAt`)
- ✅ Database schema types used (`typeof dbSchema.patient.$inferSelect`)

### **Error Handling**
- ✅ Partial success supported (some fields sync even if others fail)
- ✅ Field creation failures don't block entire sync
- ✅ Comprehensive logging at all levels (DEBUG, INFO, WARN, ERROR)

### **Request ID Tracing**
- ✅ Required `requestId` parameter for all operations
- ✅ Consistent logging with request ID throughout entire flow
- ✅ No UUID generation fallback - proper tracing enforced

### **Standard Field Mapping**
- ✅ CC custom fields ↔ AP standard fields handled correctly
- ✅ Uses centralized field mapping constants
- ✅ Bidirectional conversion with proper type handling

---

## 🔧 **Enhanced Features Added**

### **Automatic Field Creation Functions**
1. **`createCcCustomFieldPayloadWithCreation()`**
   - Creates missing CC custom fields for AP fields
   - Returns both payload and list of created fields

2. **`createCcStandardFieldPayloadWithCreation()`**
   - Creates missing CC custom fields for AP standard fields
   - Handles field name variations and international names

3. **`createApCustomFieldPayloadWithCreation()`**
   - Creates missing AP custom fields for CC fields
   - Defaults to TEXT data type for new fields

### **Robust Error Handling**
- Field creation failures logged but don't stop sync
- Database update failures properly propagated
- API call failures with descriptive error messages
- Partial success scenarios handled gracefully

### **Performance Optimizations**
- Field definitions cached during sync process
- Batch operations where possible
- Efficient database queries with proper indexing
- Minimal API calls while ensuring fresh data

---

## 📋 **Usage Examples**

### **Complete AP → CC Sync**
```typescript
import { updateApToCcCustomFields } from '@/processors/bidirectional';

// Performs complete sync with fresh data fetching and database updates
const updatedPatient = await updateApToCcCustomFields(patientInstance, requestId);
// Returns updated patient instance with fresh AP data
```

### **Complete CC → AP Sync**
```typescript
import { updateCcToApCustomFields } from '@/processors/bidirectional';

// Performs complete sync with fresh data fetching and database updates
const updatedPatient = await updateCcToApCustomFields(patientInstance, requestId);
// Returns updated patient instance with fresh CC data and updated AP data
```

### **Return Value**
Both methods now return `Promise<PatientInstance>` instead of payload objects:
- Contains fresh data from source system
- Updated timestamps
- Can be used immediately for further operations

---

## 🎯 **Implementation Gap Resolved**

**Before**: Methods only worked with cached data and created payloads for manual API updates
**After**: Methods implement complete data flow with:
- ✅ Fresh API data fetching
- ✅ Automatic field creation
- ✅ API updates
- ✅ Local database updates
- ✅ Proper error handling
- ✅ Request ID tracing

The methods are now production-ready for complete bidirectional custom field synchronization with all specified requirements met.
