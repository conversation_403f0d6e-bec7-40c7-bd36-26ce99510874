/**
 * Field Matching Utility for CC to AP Custom Fields
 *
 * Handles all field matching logic for synchronizing custom fields from CliniCore (CC)
 * to AutoPatient (AP). Provides exact matching functionality following v3Integration patterns.
 *
 * Features:
 * - Unicode-aware field name normalization and comparison
 * - Exact matching logic (no fuzzy matching)
 * - FieldKey generation and matching
 * - API fallback for enhanced field discovery
 * - Error message parsing for field identification
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { apCustomfield } from "@/apiClient";
import { logDebug, logError } from "@/utils/logger";

/**
 * Normalize string for Unicode-aware comparison
 * Handles German Umlaut characters and other special characters
 */
export function normalizeFieldName(name: string): string {
	return name
		.toLowerCase()
		.normalize("NFD")
		.replace(/[\u0300-\u036f]/g, "") // Remove diacritics
		.replace(/[^\w\s]/g, "") // Remove special characters except word chars and spaces
		.replace(/\s+/g, " ") // Normalize whitespace
		.trim();
}

/**
 * Check if two field names match using Unicode-aware comparison
 */
export function fieldNamesMatch(name1: string, name2: string): boolean {
	const normalized1 = normalizeFieldName(name1);
	const normalized2 = normalizeFieldName(name2);
	return normalized1 === normalized2;
}

/**
 * Generate AP fieldKey from CC field name
 *
 * Normalizes the field name to match AutoPatient's auto-generation pattern.
 * This function attempts to replicate how AP generates fieldKeys from field names.
 *
 * @param ccFieldName - CC field name to convert
 * @returns Normalized fieldKey that should match AP's generation
 */
export function generateApFieldKey(ccFieldName: string): string {
	// Step-by-step normalization with intermediate logging for debugging
	const step1 = ccFieldName.toLowerCase();
	const step2 = step1.normalize("NFD"); // Decompose Unicode characters
	const step3 = step2.replace(/[\u0300-\u036f]/g, ""); // Remove diacritics (ä -> a, ü -> u, etc.)
	const step4 = step3.replace(/[^a-z0-9]/g, ""); // Remove all non-alphanumeric characters
	const step5 = step4.trim();

	return step5;
}

/**
 * Find existing AP custom field using exact matching
 *
 * Follows v3Integration pattern for simple exact matching:
 * 1. Direct name matching (CC name vs AP name)
 * 2. Direct label matching (CC label vs AP name)
 * 3. Exact fieldKey matching with basic variations
 * 4. Case-insensitive exact matching as fallback
 *
 * @param apCustomFields - Array of existing AP custom fields
 * @param ccField - CC custom field to find match for
 * @param requestId - Request ID for detailed logging
 * @returns Matching AP field or undefined
 */
export function findExistingApField(
	apCustomFields: APGetCustomFieldType[],
	ccField: GetCCCustomField,
	requestId: string,
): APGetCustomFieldType | undefined {
	const expectedFieldKey = generateApFieldKey(ccField.name);

	logDebug(
		requestId,
		`Searching for exact match for CC field "${ccField.name}" (label: "${ccField.label}")`,
	);

	// Try exact name matching first (v3Integration pattern: cf.name === name)
	let match = apCustomFields.find((apField) => apField.name === ccField.name);
	if (match) {
		logDebug(
			requestId,
			`Found exact name match: "${match.name}" (ID: ${match.id})`,
		);
		return match;
	}

	// Try exact label matching (v3Integration pattern: cf.name === label)
	match = apCustomFields.find((apField) => apField.name === ccField.label);
	if (match) {
		logDebug(
			requestId,
			`Found exact label match: "${match.name}" (ID: ${match.id})`,
		);
		return match;
	}

	// Try exact fieldKey matching with basic variations
	match = apCustomFields.find(
		(apField) =>
			apField.fieldKey === expectedFieldKey ||
			apField.fieldKey === `contact.${expectedFieldKey}` ||
			apField.fieldKey?.replace("contact.", "") === expectedFieldKey,
	);
	if (match) {
		logDebug(
			requestId,
			`Found exact fieldKey match: "${match.name}" (fieldKey: "${match.fieldKey}", ID: ${match.id})`,
		);
		return match;
	}

	// Case-insensitive exact matching as final fallback
	match = apCustomFields.find(
		(apField) =>
			apField.name.toLowerCase() === ccField.name.toLowerCase() ||
			apField.name.toLowerCase() === ccField.label.toLowerCase(),
	);
	if (match) {
		logDebug(
			requestId,
			`Found case-insensitive exact match: "${match.name}" (ID: ${match.id})`,
		);
		return match;
	}

	logDebug(requestId, `No exact match found for CC field "${ccField.name}"`);
	return undefined;
}

/**
 * Enhanced field existence check with API query fallback
 * First checks local cache, then queries AP API if needed
 */
export async function findExistingApFieldWithApiCheck(
	apCustomFields: APGetCustomFieldType[],
	ccField: GetCCCustomField,
	requestId: string,
): Promise<APGetCustomFieldType | undefined> {
	// First check local cache
	let existingField = findExistingApField(apCustomFields, ccField, requestId);

	if (existingField) {
		logDebug(
			requestId,
			`Found existing field in cache: "${existingField.name}" (ID: ${existingField.id})`,
		);
		return existingField;
	}

	// If not found in cache, refresh from API to ensure we have latest data
	logDebug(
		requestId,
		`Field not found in cache, refreshing AP custom fields from API`,
	);

	try {
		const refreshedFields = await apCustomfield.all();
		logDebug(
			requestId,
			`Refreshed AP custom fields from API, total: ${refreshedFields.length}`,
		);
		existingField = findExistingApField(refreshedFields, ccField, requestId);

		if (existingField) {
			logDebug(
				requestId,
				`Found existing field after API refresh: "${existingField.name}" (ID: ${existingField.id})`,
			);
			// Update the cache with fresh data
			apCustomFields.length = 0;
			apCustomFields.push(...refreshedFields);
		}

		return existingField;
	} catch (error) {
		logError(requestId, `Failed to refresh AP custom fields:`, error);
		return undefined;
	}
}

/**
 * Extract fieldKey from AutoPatient "already exists" error message
 *
 * Parses error messages like "contact.befunde already exists" to extract the fieldKey
 *
 * @param errorMessage - Error message from AutoPatient API
 * @returns Extracted fieldKey or null if not found
 */
export function extractFieldKeyFromError(errorMessage: string): string | null {
	// Pattern to match "contact.{fieldKey} already exists" or similar
	const patterns = [
		/contact\.([a-zA-Z0-9_-]+)\s+already\s+exists/i,
		/field\s+['""]?contact\.([a-zA-Z0-9_-]+)['""]?\s+already\s+exists/i,
		/([a-zA-Z0-9_-]+)\s+already\s+exists/i, // Fallback pattern
	];

	for (const pattern of patterns) {
		const match = errorMessage.match(pattern);
		if (match && match[1]) {
			return match[1];
		}
	}

	return null;
}

/**
 * Find existing field by extracted fieldKey from error message
 *
 * Uses the fieldKey extracted from the error message to find the existing field
 *
 * @param apCustomFields - Array of AP custom fields
 * @param extractedFieldKey - FieldKey extracted from error message
 * @param requestId - Request ID for logging
 * @returns Matching field or undefined
 */
export function findFieldByExtractedKey(
	apCustomFields: APGetCustomFieldType[],
	extractedFieldKey: string,
	requestId: string,
): APGetCustomFieldType | undefined {
	logDebug(
		requestId,
		`Searching for field with extracted fieldKey: "${extractedFieldKey}"`,
	);

	const field = apCustomFields.find((apField) => {
		// Try multiple variations of the extracted fieldKey
		const fieldKeyMatches = [
			apField.fieldKey === extractedFieldKey,
			apField.fieldKey === `contact.${extractedFieldKey}`,
			apField.fieldKey?.replace("contact.", "") === extractedFieldKey,
			apField.fieldKey?.toLowerCase() === extractedFieldKey.toLowerCase(),
			apField.fieldKey?.toLowerCase() ===
				`contact.${extractedFieldKey}`.toLowerCase(),
		];

		return fieldKeyMatches.some((match) => match);
	});

	if (field) {
		logDebug(
			requestId,
			`Found field by extracted fieldKey: "${field.name}" (ID: ${field.id}, fieldKey: "${field.fieldKey}")`,
		);
	} else {
		logDebug(
			requestId,
			`No field found with extracted fieldKey: "${extractedFieldKey}"`,
		);
	}

	return field;
}
