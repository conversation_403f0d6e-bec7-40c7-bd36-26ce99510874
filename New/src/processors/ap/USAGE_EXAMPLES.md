# AP to CC Value Converter Usage Examples

This document provides practical examples of how to use the new AP to CC custom field value converter.

## Basic Usage

### Converting a Single Field Value

```typescript
import { convertApCustomFieldValue } from "./apToCcValueConverter";
import type { APGetCustomFieldType, GetCCCustomField } from "@type";

// Example AP field (boolean RADIO)
const apField: APGetCustomFieldType = {
  id: "EwFnyvpa16PQvmtb3X4b",
  name: "Befunde",
  dataType: "RADIO",
  picklistOptions: ["Yes", "No"],
  // ... other properties
};

// Example CC field (boolean)
const ccField: GetCCCustomField = {
  id: 6,
  name: "additionally-insured",
  label: "Zusatzversichert", 
  type: "boolean",
  // ... other properties
};

// Convert AP "Yes" to CC "true"
const convertedValue = convertApCustomFieldValue(
  "Yes",           // AP value
  apField,         // AP field definition
  ccField,         // CC field definition
  "req-123"        // Request ID for logging
);

console.log(convertedValue); // Output: "true"
```

### Converting Multiple Field Values

```typescript
import { batchConvertApValuesToCc } from "./apToCcValueConverter";

const apValues = [
  { fieldId: "EwFnyvpa16PQvmtb3X4b", value: "Yes" },
  { fieldId: "2BDuciugmDs5yS14iZnu", value: "Some text value" },
  { fieldId: "J2HlC6KaTLOLZwyuOu3J", value: "1, 2, 3" }
];

const convertedValues = batchConvertApValuesToCc(
  apValues,
  apCustomFields,  // Array of AP field definitions
  ccCustomFields,  // Array of CC field definitions
  "req-123"
);

// Output: Array of CC field mappings with converted values
```

## Field Type Conversion Examples

### Boolean Fields (RADIO Yes/No ↔ boolean)

```typescript
// AP RADIO field with Yes/No options
const apBooleanField: APGetCustomFieldType = {
  id: "boolean-field-id",
  name: "Is Active",
  dataType: "RADIO",
  picklistOptions: ["Yes", "No"],
  textBoxListOptions: [
    { label: "Yes", value: "Yes" },
    { label: "No", value: "No" }
  ]
};

const ccBooleanField: GetCCCustomField = {
  id: 1,
  name: "is-active",
  type: "boolean"
};

// AP → CC conversions
convertApCustomFieldValue("Yes", apBooleanField, ccBooleanField, "req-123");  // → "true"
convertApCustomFieldValue("No", apBooleanField, ccBooleanField, "req-123");   // → "false"
convertApCustomFieldValue("", apBooleanField, ccBooleanField, "req-123");     // → "false"
```

### Multi-Value Fields (MULTIPLE_OPTIONS ↔ multiselect)

```typescript
// AP MULTIPLE_OPTIONS field
const apMultiField: APGetCustomFieldType = {
  id: "multi-field-id",
  name: "Interests",
  dataType: "MULTIPLE_OPTIONS",
  picklistOptions: ["Sports", "Music", "Reading", "Travel"]
};

const ccMultiField: GetCCCustomField = {
  id: 2,
  name: "interests",
  type: "multiselect"
};

// AP → CC conversions
convertApCustomFieldValue("Sports, Music", apMultiField, ccMultiField, "req-123");     // → "Sports, Music"
convertApCustomFieldValue("Sports,Music,Reading", apMultiField, ccMultiField, "req-123"); // → "Sports, Music, Reading"
```

### Text Fields (TEXT ↔ text/textarea)

```typescript
// AP TEXT field
const apTextField: APGetCustomFieldType = {
  id: "text-field-id",
  name: "Notes",
  dataType: "TEXT"
};

const ccTextField: GetCCCustomField = {
  id: 3,
  name: "notes",
  type: "text"
};

// AP → CC conversions (direct pass-through)
convertApCustomFieldValue("Patient notes here", apTextField, ccTextField, "req-123"); // → "Patient notes here"
convertApCustomFieldValue("  Trimmed text  ", apTextField, ccTextField, "req-123");   // → "Trimmed text"
```

## Integration with Existing Sync Process

### Enhanced apToCcSync.ts Integration

The converter is already integrated into `apToCcSync.ts`. Here's how it works:

```typescript
// In matchAndCreateFields function
for (const [fieldName, fieldValue] of Object.entries(apFieldMap)) {
  // Find existing or create new CC field
  let ccField = findExistingCcField(fieldName, ccCustomFields, requestId);
  if (!ccField) {
    ccField = await createCcCustomField(fieldName, requestId);
  }

  // Find AP field definition for advanced conversion
  const apField = apCustomFields.find((f) => f.name === fieldName);
  
  // Convert value using the new bidirectional converter
  const convertedValue = apField 
    ? convertApCustomFieldValue(fieldValue, apField, ccField, requestId)
    : fieldValue; // Fallback to original value

  // Create field mapping with converted value
  const fieldMapping: PostCCPatientCustomfield = {
    field: ccField,
    values: [{ value: convertedValue }],
    patient: null,
  };
}
```

### Manual Integration Example

If you want to use the converter in a custom sync process:

```typescript
import { convertApCustomFieldValue } from "./apToCcValueConverter";
import { fieldNamesMatch } from "@/processors/cc/fieldMatcher";

async function customApToCcSync(
  apContact: GetAPContactType,
  ccPatientId: number,
  requestId: string
) {
  // Get field definitions
  const apCustomFields = await apCustomfield.all();
  const ccCustomFields = await ccCustomfieldReq.all();

  const conversions = [];

  // Process each AP custom field
  for (const apCustomField of apContact.customFields) {
    const apFieldDef = apCustomFields.find(f => f.id === apCustomField.id);
    if (!apFieldDef || !apCustomField.value) continue;

    // Find matching CC field
    const ccField = ccCustomFields.find(ccField => 
      fieldNamesMatch(apFieldDef.name, ccField.name) ||
      fieldNamesMatch(apFieldDef.name, ccField.label)
    );

    if (ccField) {
      // Convert the value
      const convertedValue = convertApCustomFieldValue(
        apCustomField.value,
        apFieldDef,
        ccField,
        requestId
      );

      conversions.push({
        customFieldId: ccField.id,
        value: convertedValue
      });
    }
  }

  // Update CC patient with converted values
  // ... implementation details
}
```

## Error Handling Examples

### Type Compatibility Warnings

```typescript
import { validateFieldTypeCompatibility } from "./apToCcValueConverter";

// Check compatibility before conversion
if (!validateFieldTypeCompatibility(apField, ccField)) {
  console.warn(`Type compatibility issue: AP ${apField.dataType} → CC ${ccField.type}`);
  // Conversion will still proceed with warning logged
}
```

### Graceful Error Handling

```typescript
try {
  const convertedValue = convertApCustomFieldValue(
    apValue,
    apField,
    ccField,
    requestId
  );
  // Use converted value
} catch (error) {
  console.error("Conversion failed:", error);
  // Fallback to original value
  const fallbackValue = apValue.trim();
}
```

## Logging and Debugging

The converter provides comprehensive logging:

```typescript
// Enable debug logging to see all conversions
import { logDebug } from "@/utils/logger";

// Conversion logs will show:
// - Field names and types
// - Original and converted values
// - Type compatibility warnings
// - Conversion applied vs pass-through

// Example log output:
// [INFO] AP→CC value conversion: Field: "Befunde" → "Zusatzversichert", Type: AP RADIO → CC boolean, Value: "Yes" → "true"
// [DEBUG] AP→CC value pass-through: Field: "Notes" → "Notes", Type: AP TEXT → CC text, Value: "Patient notes" → "Patient notes"
```

## Best Practices

1. **Always provide field definitions**: The converter works best when both AP and CC field definitions are available
2. **Handle missing fields gracefully**: Use fallback to original value when field definitions are not found
3. **Validate compatibility**: Use `validateFieldTypeCompatibility()` for critical conversions
4. **Monitor logs**: Review conversion logs to ensure data integrity
5. **Test bidirectional conversion**: Verify that CC→AP→CC round-trips preserve data integrity

## Performance Considerations

- **Batch operations**: Use `batchConvertApValuesToCc()` for multiple fields
- **Cache field definitions**: Avoid repeated API calls for field definitions
- **Lazy loading**: Only convert values when actually needed for sync
- **Error isolation**: Individual field conversion failures don't stop the entire process
