/**
 * CC Custom Field Creation Utility for AP to CC Custom Fields
 *
 * Handles creation of missing CC custom fields during AP to CC synchronization.
 * Following v3Integration pattern of creating simple TEXT fields for missing fields.
 *
 * Features:
 * - Simple CC custom field creation as TEXT type
 * - Field name normalization and validation
 * - Error handling with graceful fallbacks
 * - TypeScript compliance with proper type definitions
 */

import type { GetCCCustomField, PostCCCustomField } from "@type";
import { ccCustomfieldReq } from "@/apiClient";
import { logDebug, logError, logInfo } from "@/utils/logger";

/**
 * Create a new CC custom field with TEXT type
 * Following v3Integration pattern of creating simple TEXT fields for missing AP fields
 *
 * @param fieldName - Name of the field to create (will be used for both name and label)
 * @param requestId - Request ID for logging
 * @returns Promise<GetCCCustomField> - Created CC custom field
 */
export async function createCcCustomField(
	fieldName: string,
	requestId: string,
): Promise<GetCCCustomField> {
	// Normalize field name for CC (remove special characters, spaces, etc.)
	const normalizedName = normalizeFieldName(fieldName);
	const displayLabel = fieldName; // Keep original name as display label

	logInfo(
		requestId,
		`Creating new CC custom field: "${displayLabel}" with name: "${normalizedName}" (type: TEXT)`,
	);

	const createData: PostCCCustomField = {
		name: normalizedName,
		label: displayLabel,
		type: "text", // Always create as TEXT type (v3Integration pattern)
		validation: "",
		allowMultipleValues: false,
		isRequired: false,
	};

	try {
		const ccCustomField = await ccCustomfieldReq.create(createData);

		logInfo(
			requestId,
			`Successfully created CC custom field: "${displayLabel}" with ID: ${ccCustomField.id}`,
		);

		return ccCustomField;
	} catch (createError: unknown) {
		const errorMessage =
			createError instanceof Error ? createError.message : String(createError);

		// Handle duplicate field name error
		if (
			errorMessage.includes("already exists") ||
			errorMessage.includes("duplicate")
		) {
			logInfo(
				requestId,
				`Field creation failed - already exists. Error: "${errorMessage}"`,
			);

			// Try to find the existing field
			try {
				const existingFields = await ccCustomfieldReq.all(true); // Force cache refresh
				const existingField = existingFields.find(
					(field) =>
						field.name === normalizedName ||
						field.label === displayLabel ||
						field.name.toLowerCase() === normalizedName.toLowerCase() ||
						field.label.toLowerCase() === displayLabel.toLowerCase(),
				);

				if (existingField) {
					logInfo(
						requestId,
						`Found existing CC custom field: "${existingField.label}" with ID: ${existingField.id}`,
					);
					return existingField;
				}
			} catch (fetchError) {
				logError(
					requestId,
					"Failed to fetch existing CC custom fields after creation failure",
					fetchError,
				);
			}
		}

		logError(
			requestId,
			`Failed to create CC custom field "${displayLabel}":`,
			createError,
		);
		throw new Error(
			`Failed to create CC custom field "${displayLabel}": ${errorMessage}`,
		);
	}
}

/**
 * Normalize field name for CC custom field creation
 * Removes special characters and ensures valid field name format
 *
 * @param fieldName - Original field name
 * @returns Normalized field name suitable for CC
 */
function normalizeFieldName(fieldName: string): string {
	return (
		fieldName
			.trim()
			.toLowerCase()
			// Replace spaces and special characters with underscores
			.replace(/[^a-z0-9]/g, "_")
			// Remove multiple consecutive underscores
			.replace(/_+/g, "_")
			// Remove leading/trailing underscores
			.replace(/^_+|_+$/g, "") ||
		// Ensure it's not empty
		"custom_field"
	);
}

/**
 * Check if a CC custom field exists by name or label
 * Uses case-insensitive matching for better compatibility
 *
 * @param fieldName - Field name to search for
 * @param ccCustomFields - Array of existing CC custom fields
 * @param requestId - Request ID for logging
 * @returns Matching CC field or undefined
 */
export function findExistingCcField(
	fieldName: string,
	ccCustomFields: GetCCCustomField[],
	requestId: string,
): GetCCCustomField | undefined {
	const normalizedSearchName = fieldName.toLowerCase().trim();

	// Try exact name match first
	let match = ccCustomFields.find(
		(field) => field.name.toLowerCase() === normalizedSearchName,
	);

	if (match) {
		logDebug(requestId, `Found CC field by exact name match: "${match.label}"`);
		return match;
	}

	// Try exact label match
	match = ccCustomFields.find(
		(field) => field.label.toLowerCase() === normalizedSearchName,
	);

	if (match) {
		logDebug(
			requestId,
			`Found CC field by exact label match: "${match.label}"`,
		);
		return match;
	}

	// Try normalized name match
	const normalizedFieldName = normalizeFieldName(fieldName);
	match = ccCustomFields.find(
		(field) => field.name.toLowerCase() === normalizedFieldName,
	);

	if (match) {
		logDebug(
			requestId,
			`Found CC field by normalized name match: "${match.label}"`,
		);
		return match;
	}

	logDebug(requestId, `No CC custom field match found for: "${fieldName}"`);
	return undefined;
}
