/**
 * AP to CC Type Conversion Utilities
 *
 * Handles bidirectional type conversion between AutoPatient (AP) and CliniCore (CC) custom fields.
 * This module provides the inverse conversion logic for the CC to AP mappings found in
 * ccToApCustomFieldsProcessor.ts and customFieldCreator.ts.
 *
 * Key Features:
 * - AP to CC field type mapping (inverse of CC_TO_AP_DATA_TYPE_MAPPING)
 * - Value transformation for boolean fields (AP RADIO Yes/No → CC boolean true/false)
 * - Detection of boolean RADIO fields based on Yes/No options
 * - Graceful handling of unsupported conversions
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { logDebug, logInfo } from "@/utils/logger";

/**
 * AP to CC field type mapping
 * Enhanced mapping based on v3Integration analysis to ensure comprehensive coverage
 * Inverse of the CC_TO_AP_DATA_TYPE_MAPPING from customFieldCreator.ts
 */
export const AP_TO_CC_TYPE_MAPPING: Record<string, string> = {
	// Text-based fields (most common and reliable)
	TEXT: "text",
	LARGE_TEXT: "textarea",
	STRING: "text", // Additional text variant

	// Numeric fields
	NUMERICAL: "number",
	FLOAT: "decimal",
	MONETORY: "currency",
	CURRENCY: "currency", // Alternative spelling
	MONEY: "currency", // Additional currency variant

	// Contact fields
	PHONE: "phone",
	EMAIL: "email", // Add email field support

	// Boolean fields - RADIO can be boolean if it has Yes/No options
	CHECKBOX: "boolean",
	RADIO: "boolean", // Will be validated by checking options
	BOOLEAN: "boolean", // Direct boolean mapping

	// Selection fields
	SINGLE_OPTIONS: "select",
	MULTIPLE_OPTIONS: "multiselect",
	DROPDOWN: "select", // Alternative dropdown mapping
	SELECT: "select", // Direct select mapping
	MULTISELECT: "multiselect", // Direct multiselect mapping

	// Date/Time fields
	DATE: "date",
	DATETIME: "date", // Map datetime to date for compatibility
	TIME: "text", // Time fields as text (v3Integration pattern)

	// File fields
	FILE_UPLOAD: "file",
	FILE: "file", // Direct file mapping
	UPLOAD: "file", // Alternative upload mapping
	ATTACHMENT: "file", // Alternative attachment mapping

	// Signature
	SIGNATURE: "signature",

	// Additional field types found in v3Integration analysis
	URL: "text", // URLs as text fields
	WEBSITE: "text", // Website fields as text
	ADDRESS: "text", // Address fields as text
	TEXTAREA: "textarea", // Direct textarea mapping
	TEXTBOX: "text", // Textbox as text
	INPUT: "text", // Generic input as text

	// Fallback for unknown types (v3Integration approach)
	DEFAULT: "text", // Default to text for maximum compatibility
};

/**
 * Check if an AP RADIO field represents a boolean field
 * Boolean RADIO fields are identified by having exactly two options: "Yes" and "No"
 *
 * @param apField - AP custom field to check
 * @returns true if the field is a boolean RADIO field
 */
export function isApRadioBooleanField(apField: APGetCustomFieldType): boolean {
	if (apField.dataType !== "RADIO") {
		return false;
	}

	// Check if field has exactly Yes/No options
	const options = apField.textBoxListOptions;
	if (!options || options.length !== 2) {
		return false;
	}

	const labels = options.map((opt) => opt.label.toLowerCase().trim());
	return labels.includes("yes") && labels.includes("no");
}

/**
 * Get the expected CC field type for an AP field type
 * Enhanced with v3Integration-style fallback logic for maximum compatibility
 *
 * @param apDataType - AP field data type
 * @param apField - AP field definition (for boolean RADIO detection)
 * @returns Expected CC field type, defaults to "text" for unknown types (v3Integration pattern)
 */
export function getExpectedCcFieldType(
	apDataType: string,
	apField?: APGetCustomFieldType,
): string {
	// Special handling for RADIO fields
	if (apDataType === "RADIO" && apField && isApRadioBooleanField(apField)) {
		return "boolean";
	}

	// Try direct mapping first
	const mappedType = AP_TO_CC_TYPE_MAPPING[apDataType];
	if (mappedType) {
		return mappedType;
	}

	// Fallback to text for unknown types (v3Integration approach)
	// This ensures maximum compatibility - any unknown AP field type can sync as text
	return AP_TO_CC_TYPE_MAPPING.DEFAULT || "text";
}

/**
 * Log field type conversion information for debugging
 *
 * @param apField - AP custom field
 * @param ccField - CC custom field
 * @param value - Value being converted
 * @param transformedValue - Value after conversion
 * @param requestId - Request ID for logging
 */
export function logTypeConversion(
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	value: string,
	transformedValue: string,
	requestId: string,
): void {
	const conversionInfo = [
		`Field: "${apField.name}" → "${ccField.label}"`,
		`Type: AP ${apField.dataType} → CC ${ccField.type}`,
		`Value: "${value}" → "${transformedValue}"`,
	].join(", ");

	if (value !== transformedValue) {
		logInfo(requestId, `Type conversion applied: ${conversionInfo}`);
	} else {
		logDebug(requestId, `Type conversion (no change): ${conversionInfo}`);
	}
}
