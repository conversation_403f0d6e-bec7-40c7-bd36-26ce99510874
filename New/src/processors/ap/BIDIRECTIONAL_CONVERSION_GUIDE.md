# Bidirectional Custom Field Conversion Guide

This document explains how the bidirectional conversion system works between AutoPatient (AP) and CliniCore (CC) custom fields, ensuring data integrity and proper synchronization in both directions.

## Overview

The bidirectional conversion system consists of two main components:

1. **CC → AP Conversion** (existing): `ccToApCustomFieldsProcessor.ts`
2. **AP → CC Conversion** (new): `apToCcValueConverter.ts`

These components work together to ensure that custom field values can be synchronized in both directions while maintaining data integrity and handling type differences between the systems.

## Conversion Matrix

| AP Field Type | CC Field Type | AP → CC Conversion | CC → AP Conversion |
|---------------|---------------|-------------------|-------------------|
| RADIO (Yes/No) | boolean | Yes→true, No→false | true→Yes, false→No |
| TEXT | text | Direct pass-through | Direct pass-through |
| LARGE_TEXT | textarea | Direct pass-through | Direct pass-through |
| MULTIPLE_OPTIONS | multiselect | Parse comma-separated | Join with comma |
| SINGLE_OPTIONS | select | Direct pass-through | Direct pass-through |
| CHECKBOX | boolean | Direct boolean conversion | true→Yes, false→No |
| NUMERICAL | number | Direct pass-through | Direct pass-through |
| PHONE | phone | Direct pass-through | Direct pass-through |
| EMAIL | email | Direct pass-through | Direct pass-through |

## Boolean Field Conversion

### CC → AP (Existing Logic)
```typescript
// From ccToApCustomFieldsProcessor.ts transformBooleanValue()
function transformBooleanValue(value: string, fieldType: string): string {
  if (fieldType.toLowerCase() === "boolean") {
    const normalizedValue = value.toLowerCase().trim();
    if (normalizedValue === "true" || normalizedValue === "1" || normalizedValue === "yes") {
      return "Yes";
    } else if (normalizedValue === "false" || normalizedValue === "0" || normalizedValue === "no") {
      return "No";
    }
    return "No"; // Default to "No"
  }
  return value; // Pass-through for non-boolean fields
}
```

### AP → CC (New Logic)
```typescript
// From apToCcValueConverter.ts convertApBooleanToCc()
function convertApBooleanToCc(apValue: string): string {
  const normalizedValue = apValue.toLowerCase().trim();
  if (normalizedValue === "yes") {
    return "true";
  } else if (normalizedValue === "no") {
    return "false";
  }
  return "false"; // Default to false (matches CC→AP behavior)
}
```

## Boolean Field Detection

### AP Boolean RADIO Detection
```typescript
// From typeConverter.ts isApRadioBooleanField()
function isApRadioBooleanField(apField: APGetCustomFieldType): boolean {
  if (apField.dataType !== "RADIO") return false;
  
  const options = apField.textBoxListOptions;
  if (!options || options.length !== 2) return false;
  
  const labels = options.map(opt => opt.label.toLowerCase().trim());
  return labels.includes("yes") && labels.includes("no");
}
```

This ensures that only RADIO fields with exactly "Yes" and "No" options are treated as boolean fields.

## Multi-Value Field Handling

### CC → AP (Existing Logic)
```typescript
// From ccToApCustomFieldsProcessor.ts extractFieldValue()
function extractFieldValue(ccCustomField: GetCCPatientCustomField): string {
  const values = ccCustomField.values.map(v => v.value.trim()).filter(v => v.length > 0);
  
  if (values.length === 0) return "";
  if (values.length === 1) return values[0];
  
  const fieldType = ccCustomField.field.type?.toLowerCase() || "";
  
  if (fieldType.includes("multiselect") || fieldType.includes("checkbox")) {
    return values.join(", "); // Comma separation for multi-select
  } else if (fieldType.includes("textarea") || fieldType.includes("text")) {
    return values.join("\n"); // Newline separation for text areas
  } else {
    return values.join(", "); // Default to comma separation
  }
}
```

### AP → CC (New Logic)
```typescript
// From apToCcValueConverter.ts convertApMultiValueToCc()
function convertApMultiValueToCc(apValue: string, ccFieldType: string): string {
  const normalizedType = ccFieldType.toLowerCase();
  
  if (normalizedType.includes("multiselect") || normalizedType.includes("checkbox")) {
    // Split by comma, trim each value, and rejoin
    const values = apValue.split(",").map(v => v.trim()).filter(v => v.length > 0);
    return values.join(", ");
  }
  
  return apValue.trim(); // For other field types, return as-is
}
```

## Field Matching Strategy

Both conversion directions use the same field matching logic:

```typescript
// From fieldMatcher.ts fieldNamesMatch()
function fieldNamesMatch(name1: string, name2: string): boolean {
  const normalized1 = normalizeFieldName(name1);
  const normalized2 = normalizeFieldName(name2);
  return normalized1 === normalized2;
}

function normalizeFieldName(name: string): string {
  return name
    .toLowerCase()
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "") // Remove diacritics
    .replace(/[^\w\s]/g, "") // Remove special characters
    .replace(/\s+/g, " ") // Normalize whitespace
    .trim();
}
```

This ensures Unicode-aware matching that handles German umlauts and other special characters.

## Type Compatibility Validation

The new AP → CC converter includes type compatibility validation:

```typescript
function validateFieldTypeCompatibility(
  apField: APGetCustomFieldType,
  ccField: GetCCCustomField
): boolean {
  const expectedCcType = getExpectedCcFieldType(apField.dataType, apField);
  const actualCcType = ccField.type.toLowerCase().trim();
  
  // Direct type match
  if (expectedCcType === actualCcType) return true;
  
  // Special compatibility cases
  const compatibilityMap: Record<string, string[]> = {
    text: ["textarea", "string", "varchar"],
    textarea: ["text", "string"],
    boolean: ["select", "radio"],
    select: ["text"],
    multiselect: ["text", "textarea"],
    // ... more mappings
  };
  
  const compatibleTypes = compatibilityMap[expectedCcType] || [];
  return compatibleTypes.includes(actualCcType);
}
```

## Integration with Existing Code

### Using the New Converter in apToCcSync.ts

The new converter can be integrated into the existing `apToCcSync.ts` file:

```typescript
import { convertApCustomFieldValue } from "./apToCcValueConverter";

// In matchAndCreateFields function:
for (const [fieldName, fieldValue] of Object.entries(apFieldMap)) {
  // Find AP field definition
  const apField = apCustomFields.find(f => f.name === fieldName);
  
  // Find or create CC field
  let ccField = findExistingCcField(fieldName, ccCustomFields, requestId);
  if (!ccField) {
    ccField = await createCcCustomField(fieldName, requestId);
  }
  
  // Convert value using the new converter
  const convertedValue = apField 
    ? convertApCustomFieldValue(fieldValue, apField, ccField, requestId)
    : fieldValue; // Fallback to original value if AP field not found
  
  matchedFields.push({
    customFieldId: ccField.id,
    value: convertedValue,
  });
}
```

## Error Handling and Logging

Both conversion directions include comprehensive error handling:

1. **Input Validation**: Check for empty values and invalid field definitions
2. **Type Compatibility Warnings**: Log warnings for potentially incompatible field types
3. **Conversion Logging**: Log all value transformations for audit purposes
4. **Graceful Fallbacks**: Continue processing even if individual field conversions fail

## Testing the Bidirectional Conversion

To verify bidirectional conversion works correctly:

1. **Round-trip Test**: CC value → AP → CC should return original value
2. **Boolean Test**: CC boolean true → AP "Yes" → CC "true"
3. **Multi-value Test**: CC multiselect "A, B, C" → AP "A, B, C" → CC "A, B, C"
4. **Field Matching Test**: Ensure fields match correctly in both directions

## Maintenance Notes

When adding new field types or conversion logic:

1. Update both conversion directions
2. Add corresponding entries to the type mapping
3. Update the compatibility validation
4. Add comprehensive logging
5. Test bidirectional conversion thoroughly

This ensures the synchronization system remains robust and maintains data integrity across both systems.
