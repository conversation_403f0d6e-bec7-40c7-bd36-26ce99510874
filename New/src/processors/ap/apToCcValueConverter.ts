/**
 * AP to CC Custom Field Value Converter
 *
 * Provides dynamic, field-name agnostic conversion of AutoPatient (AP) custom field values
 * to CliniCore (CC) custom field values for bidirectional synchronization.
 *
 * This module implements the inverse conversion logic of the CC to AP transformations
 * found in ccToApCustomFieldsProcessor.ts, ensuring data integrity and proper
 * bidirectional synchronization between the two systems.
 *
 * Key Features:
 * - Bidirectional value conversion tracking (reverses CC→AP transformations)
 * - Boolean field conversion: AP RADIO (Yes/No) ↔ CC boolean (true/false)
 * - Multi-value field handling: AP MULTIPLE_OPTIONS ↔ CC multiselect
 * - Field type compatibility validation
 * - Field-name agnostic operation (works with any field names)
 * - Comprehensive logging and error handling
 * - TypeScript compliance without 'any' types
 *
 * Conversion Matrix:
 * ┌─────────────────────┬─────────────────────┬─────────────────────┐
 * │ AP Field Type       │ CC Field Type       │ Value Transformation│
 * ├─────────────────────┼─────────────────────┼─────────────────────┤
 * │ RADIO (Yes/No)      │ boolean             │ Yes→true, No→false │
 * │ TEXT/LARGE_TEXT     │ text/textarea       │ Direct pass-through │
 * │ MULTIPLE_OPTIONS    │ multiselect         │ Parse comma-separated│
 * │ SINGLE_OPTIONS      │ select              │ Direct pass-through │
 * │ CHECKBOX            │ boolean             │ Direct boolean conv │
 * │ NUMERICAL/FLOAT     │ number/decimal      │ Direct pass-through │
 * │ PHONE/EMAIL         │ phone/email         │ Direct pass-through │
 * └─────────────────────┴─────────────────────┴─────────────────────┘
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { logDebug, logInfo, logWarn } from "@/utils/logger";
import {
	isApRadioBooleanField,
	getExpectedCcFieldType,
} from "./typeConverter";

/**
 * Convert AP boolean RADIO field value to CC boolean value
 * Implements the inverse of transformBooleanValue from ccToApCustomFieldsProcessor.ts
 *
 * @param apValue - AP field value ("Yes" or "No")
 * @returns CC boolean value ("true" or "false")
 */
export function convertApBooleanToCc(apValue: string): string {
	const normalizedValue = apValue.toLowerCase().trim();

	// Convert AP Yes/No to CC boolean
	if (normalizedValue === "yes") {
		return "true";
	} else if (normalizedValue === "no") {
		return "false";
	}

	// Default to false for any other value (matches CC→AP behavior)
	return "false";
}

/**
 * Convert AP multi-value field to CC format
 * Handles MULTIPLE_OPTIONS fields that may have comma-separated values
 *
 * @param apValue - AP field value (potentially comma-separated)
 * @param ccFieldType - CC field type to determine output format
 * @returns Formatted value for CC field
 */
export function convertApMultiValueToCc(
	apValue: string,
	ccFieldType: string,
): string {
	const normalizedType = ccFieldType.toLowerCase();

	// For multiselect fields, ensure proper comma separation
	if (normalizedType.includes("multiselect") || normalizedType.includes("checkbox")) {
		// Split by comma, trim each value, and rejoin
		const values = apValue
			.split(",")
			.map((v) => v.trim())
			.filter((v) => v.length > 0);
		return values.join(", ");
	}

	// For other field types, return as-is
	return apValue.trim();
}

/**
 * Validate that AP and CC field types are compatible for conversion
 * Uses the type mapping from typeConverter.ts to check compatibility
 *
 * @param apField - AP custom field definition
 * @param ccField - CC custom field definition
 * @returns true if fields are compatible for conversion
 */
export function validateFieldTypeCompatibility(
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
): boolean {
	const expectedCcType = getExpectedCcFieldType(apField.dataType, apField);
	const actualCcType = ccField.type.toLowerCase().trim();

	// Direct type match
	if (expectedCcType === actualCcType) {
		return true;
	}

	// Special compatibility cases (following v3Integration patterns)
	const compatibilityMap: Record<string, string[]> = {
		text: ["textarea", "string", "varchar"],
		textarea: ["text", "string"],
		boolean: ["select", "radio"], // Boolean can work with select fields
		select: ["text"], // Select values can be stored as text
		multiselect: ["text", "textarea"], // Multi-select can be stored as text
		number: ["text", "decimal", "float"],
		decimal: ["number", "text"],
		phone: ["text", "string"],
		email: ["text", "string"],
	};

	const compatibleTypes = compatibilityMap[expectedCcType] || [];
	return compatibleTypes.includes(actualCcType);
}

/**
 * Log value conversion details for debugging and audit purposes
 *
 * @param apField - AP custom field definition
 * @param ccField - CC custom field definition
 * @param originalValue - Original AP value
 * @param convertedValue - Converted CC value
 * @param requestId - Request ID for logging
 */
export function logValueConversion(
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	originalValue: string,
	convertedValue: string,
	requestId: string,
): void {
	const conversionInfo = [
		`Field: "${apField.name}" → "${ccField.label}"`,
		`Type: AP ${apField.dataType} → CC ${ccField.type}`,
		`Value: "${originalValue}" → "${convertedValue}"`,
	].join(", ");

	if (originalValue !== convertedValue) {
		logInfo(requestId, `AP→CC value conversion: ${conversionInfo}`);
	} else {
		logDebug(requestId, `AP→CC value pass-through: ${conversionInfo}`);
	}
}

/**
 * Main conversion function: Convert AP custom field value to CC format
 * 
 * This is the primary function for converting AP custom field values to CC format.
 * It handles all field types and implements bidirectional conversion tracking.
 *
 * @param apValue - AP custom field value to convert
 * @param apField - AP custom field definition
 * @param ccField - CC custom field definition
 * @param requestId - Request ID for logging
 * @returns Converted value suitable for CC custom field
 */
export function convertApValueToCc(
	apValue: string,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string,
): string {
	// Input validation
	if (!apValue || apValue.trim().length === 0) {
		logDebug(requestId, `Empty AP value for field "${apField.name}"`);
		return "";
	}

	const trimmedValue = apValue.trim();

	try {
		// Validate field type compatibility
		if (!validateFieldTypeCompatibility(apField, ccField)) {
			logWarn(
				requestId,
				`Type compatibility warning: AP ${apField.dataType} → CC ${ccField.type} for field "${apField.name}"`,
			);
			// Continue with conversion despite compatibility warning
		}

		let convertedValue = trimmedValue;

		// Handle boolean RADIO fields (AP Yes/No → CC true/false)
		if (isApRadioBooleanField(apField) && ccField.type.toLowerCase() === "boolean") {
			convertedValue = convertApBooleanToCc(trimmedValue);
		}
		// Handle multi-value fields
		else if (
			apField.dataType === "MULTIPLE_OPTIONS" &&
			(ccField.type.toLowerCase().includes("multiselect") ||
				ccField.type.toLowerCase().includes("checkbox"))
		) {
			convertedValue = convertApMultiValueToCc(trimmedValue, ccField.type);
		}
		// Handle other field types (direct pass-through with trimming)
		else {
			convertedValue = trimmedValue;
		}

		// Log the conversion
		logValueConversion(apField, ccField, trimmedValue, convertedValue, requestId);

		return convertedValue;
	} catch (error) {
		logWarn(
			requestId,
			`Error converting AP value for field "${apField.name}": ${error}. Using original value.`,
		);
		return trimmedValue;
	}
}

/**
 * Convenience function for integration with existing apToCcSync.ts
 * Provides a simple interface for converting AP custom field values
 *
 * @param apValue - AP custom field value
 * @param apField - AP custom field definition
 * @param ccField - CC custom field definition
 * @param requestId - Request ID for logging
 * @returns Converted value for CC custom field
 */
export function convertApCustomFieldValue(
	apValue: string,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string,
): string {
	return convertApValueToCc(apValue, apField, ccField, requestId);
}

/**
 * Batch convert multiple AP custom field values to CC format
 * Useful for processing multiple fields in a single operation
 *
 * @param apValues - Array of AP field value mappings
 * @param apFields - Array of AP custom field definitions
 * @param ccFields - Array of CC custom field definitions
 * @param requestId - Request ID for logging
 * @returns Array of converted CC field mappings
 */
export interface ApFieldValueMapping {
	fieldId: string;
	value: string;
}

export interface CcFieldValueMapping {
	fieldId: number;
	value: string;
}

export function batchConvertApValuesToCc(
	apValues: ApFieldValueMapping[],
	apFields: APGetCustomFieldType[],
	ccFields: GetCCCustomField[],
	requestId: string,
): CcFieldValueMapping[] {
	const convertedValues: CcFieldValueMapping[] = [];

	for (const apValue of apValues) {
		const apField = apFields.find((f) => f.id === apValue.fieldId);
		if (!apField) {
			logWarn(requestId, `AP field not found for ID: ${apValue.fieldId}`);
			continue;
		}

		// Find matching CC field (this would use fieldNamesMatch in practice)
		const ccField = ccFields.find((f) => 
			f.name.toLowerCase() === apField.name.toLowerCase() ||
			f.label.toLowerCase() === apField.name.toLowerCase()
		);

		if (!ccField) {
			logWarn(requestId, `No matching CC field found for AP field: ${apField.name}`);
			continue;
		}

		const convertedValue = convertApValueToCc(
			apValue.value,
			apField,
			ccField,
			requestId,
		);

		convertedValues.push({
			fieldId: ccField.id,
			value: convertedValue,
		});
	}

	logInfo(
		requestId,
		`Batch converted ${convertedValues.length}/${apValues.length} AP values to CC format`,
	);

	return convertedValues;
}
