import { Hono } from "hono";
import { contextStorage } from "hono/context-storage";
import { requestId } from "hono/request-id";
import { handleErrorCleanup } from "@/handlers/adminHandler";
import { handleAPWebhook } from "@/handlers/apHandler";
import { handleCCWebhook } from "@/handlers/ccHandler";
import { logError } from "@/utils/logger";

const app = new Hono<Env>();
app.use(contextStorage());
app.use("*", requestId());

app.onError((err, c) => {
	const requestId = c.get("requestId") || "unknown";
	logError(requestId, "Unhandled application error", err);
	return c.json(
		{
			message: "Internal Server Error",
			requestId,
			timestamp: new Date().toISOString(),
		},
		500,
	);
});

// Health check endpoints
app.get("/", (c) => c.text("DermaCare Data Sync Service - OK"));

// Webhook endpoints
app.post("/webhooks/ap", handleAPWebhook);
app.post("/webhooks/cc", handleCCWebhook);

// Admin error cleanup endpoint
app.get("/admin/cleanup-errors", handleErrorCleanup);

export default app;
